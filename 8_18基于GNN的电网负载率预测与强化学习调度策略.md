# 基于GNN的电网负载率预测与强化学习调度策略算法设计

## 核心算法架构

### 整体架构设计
```
电网数据+位置编码+PTDF属性 → GNN负载率预测 → Actor网络(GNN backbone) → 调度策略 → Critic评估 → 策略优化
```

**核心创新**：将GNN预测网络作为强化学习Actor的backbone，实现预测与决策的深度融合。这种设计的优势在于：
- **信息共享**：预测和决策共享同一套特征表示，避免信息丢失
- **端到端优化**：两个任务可以相互促进，提升整体性能
- **计算效率**：共享计算减少了推理时间，满足实时调度需求

### 输入特征设计

#### 多层次位置编码（创新点）
**设计理念**：电网节点的位置信息对负载率预测至关重要，传统方法只考虑地理位置，本方案创新性地融合三种位置信息：

- **绝对位置编码**：基于地理坐标的正弦余弦编码
  $$PE_{pos,2i} = \sin(pos/10000^{2i/d})$$
  $$PE_{pos,2i+1} = \cos(pos/10000^{2i/d})$$
- **相对位置编码**：节点间的电气距离编码
- **拓扑位置编码**：基于图结构的节点中心性编码

**优势分析**：
- **空间感知增强**：三种位置编码互补，全面刻画节点的空间特性
- **泛化能力提升**：相对位置编码使模型能够适应不同规模的电网
- **物理意义明确**：电气距离比地理距离更能反映电网中的功率传输关系

#### PTDF物理约束融入（创新点）
**设计理念**：将电力系统专业知识PTDF（功率传输分布因子）融入神经网络，确保预测结果符合物理规律。

**PTDF特征设计**：
- **节点注入功率敏感度**：该节点功率变化对各线路潮流的影响系数
- **关键线路PTDF值**：该节点对系统关键线路的影响程度
- **线路PTDF系数向量**：该线路对应的完整PTDF系数向量

**优势分析**：

- **物理合理性**：确保预测结果符合电力系统潮流分布规律
- **预测精度提升**：物理约束指导网络学习正确的因果关系
- **可解释性增强**：PTDF系数提供了预测结果的物理解释

## GNN负载率预测算法

### GAT网络架构设计

#### 网络选择理由
选择GAT（Graph Attention Network）作为主干网络的原因：
- **自适应权重学习**：注意力机制能够自动学习节点间的重要性权重
- **多头注意力机制**：可以捕捉不同类型的节点关系（电气耦合、地理邻近、功能相似）
- **拓扑鲁棒性**：对图结构变化具有较好的适应性，适合电网拓扑的动态变化
- **可解释性强**：注意力权重可以解释节点间的影响关系

#### 网络架构设计
**特征编码层**：
- 节点编码器：62维原始特征 → 128维统一表示
- 边编码器：变长边特征 → 64维表示
- 全局编码器：56维系统特征 → 32维全局上下文

**三层GAT主干网络**：
- **第1层GAT**：128维 → 128维，8个注意力头，学习直接邻居关系
- **第2层GAT**：128维 → 128维，6个注意力头，学习二跳邻居关系
- **第3层GAT**：128维 → 64维，4个注意力头，学习全局结构关系

### 创新损失函数设计

#### 复合损失函数（核心创新）
**设计理念**：本方案创新性地设计了四项损失的组合，确保预测结果既准确又符合物理规律。

**主要损失**：加权均方误差损失
$$L_{main} = \frac{1}{M} \sum_{i=1}^{M} w_i (y_{pred,i} - y_{true,i})^2$$
其中 $w_i = 1 + 3 \cdot \mathbb{1}_{y_{true,i} > 0.95}$

**物理约束损失**（创新点）：
$$L_{physics} = \frac{1}{N \times M} \sum_{i=1}^{N} \sum_{j=1}^{M} (PTDF_{pred,ij} - PTDF_{true,ij})^2$$

**辅助任务损失**：过载检测的二分类交叉熵损失
$$L_{aux} = -\frac{1}{M} \sum_{i=1}^{M} [y_{overload,i} \log p_{overload,i} + (1-y_{overload,i}) \log(1-p_{overload,i})]$$

**注意力正则化损失**（创新点）：
$$L_{attention} = -\frac{1}{H \times N^2} \sum_{h=1}^{H} \sum_{i=1}^{N} \sum_{j=1}^{N} \alpha_{h,i,j} \log \alpha_{h,i,j}$$

**总损失函数**：
$$L_{total} = L_{main} + 0.1 \times L_{physics} + 0.5 \times L_{aux} + 0.01 \times L_{attention}$$

#### 创新优势分析
**物理约束损失的优势**：
- **物理合理性保证**：确保预测的潮流分布符合PTDF物理约束
- **泛化能力提升**：物理约束作为正则化项，提高模型在未见场景下的表现
- **预测精度改善**：物理知识指导网络学习正确的因果关系

**注意力正则化的优势**：
- **防止过度集中**：避免注意力权重过度集中在少数节点上
- **提升鲁棒性**：使模型能够关注到更多相关节点，提高鲁棒性
- **增强可解释性**：更均匀的注意力分布提供更好的可解释性

**加权损失的优势**：
- **关键场景重点关注**：对过载线路给予更高权重，提升安全关键场景的预测精度
- **样本不平衡处理**：缓解正常运行样本远多于过载样本的不平衡问题

### 训练算法流程

#### 训练流程伪代码
```
算法：GNN负载率预测模型训练
输入：训练数据集D_train，验证数据集D_val，超参数配置H
输出：训练好的GNN模型M

初始化阶段：
1. 创建GAT模型M，包含特征编码器、三层GAT和输出头
2. 创建AdamW优化器，学习率lr=1e-4，权重衰减wd=1e-5
3. 创建余弦退火学习率调度器，周期T=100
4. 设置最佳验证损失best_val_loss为无穷大
5. 设置早停耐心值patience_counter为0，最大耐心值max_patience为50

主训练循环：
对于每个训练轮次epoch从1到max_epochs：
  设置模型为训练模式
  初始化当前轮次训练损失为0

  对于训练数据加载器中的每个批次：
    提取批次数据：节点特征X、边特征E、邻接矩阵A、真实标签Y

    前向传播阶段：
    - 通过节点编码器处理62维节点特征得到128维表示
    - 通过边编码器处理变长边特征得到64维表示
    - 通过全局编码器处理56维系统特征得到32维上下文
    - 第1层GAT：计算8头注意力权重，聚合邻居信息
    - 第2层GAT：计算6头注意力权重，扩展感受野
    - 第3层GAT：计算4头注意力权重，全局信息整合
    - 输出层：生成负载率预测和过载概率

    损失计算阶段：
    - 计算主要损失：加权均方误差，过载线路权重
    - 计算物理约束损失：预测PTDF与真实PTDF的均方误差
    - 计算辅助任务损失：过载检测的交叉熵损失
    - 计算注意力正则化：注意力权重的熵正则化
    - 组合总损失：四项损失按权重组合

    反向传播阶段：
    - 计算梯度：对总损失进行反向传播
    - 梯度裁剪：限制梯度范数在0.5以内防止梯度爆炸
    - 参数更新：使用AdamW优化器更新所有参数
    - 清零梯度：为下一次迭代准备

    累积训练损失

  更新学习率调度器

  验证阶段：
  设置模型为评估模式
  计算验证集上的损失和各项指标

  模型保存与早停：
  如果验证损失改善：
    保存当前最佳模型检查点
    重置耐心计数器为0
  否则：
    增加耐心计数器
    如果超过最大耐心值：
      停止训练，加载最佳模型检查点
```
- **历史运行数据**：收集电网3年历史运行数据，包含正常、故障、极端天气等多种场景
- **拓扑数据**：电网节点连接关系、线路参数、设备额定参数
- **气象数据**：温度、湿度、风速等影响线路载流量的环境因素
- **负荷预测数据**：各节点的负荷预测值和实际值
- **发电计划数据**：各发电机的出力计划和实际出力

#### 图数据构建
**节点定义**：
- 发电机节点：火电、水电、风电、光伏等发电设备
- 负荷节点：工业、商业、居民等用电负荷
- 变电站节点：电压变换和潮流汇集点

**边定义**：
- 输电线路：高压交流线路、直流线路
- 变压器：电压等级变换设备
- 开关设备：断路器、隔离开关等

**节点特征构建**：
- 静态特征：[节点类型, 额定容量, 电压等级, 经度, 纬度, 海拔]
- 动态特征：[有功功率, 无功功率, 电压幅值, 电压相角, 负载率]
- 位置编码：[绝对位置编码(8维), 相对位置编码(8维), 拓扑位置编码(8维)]
- PTDF特征：[关键线路PTDF值(10维), 区域影响系数(5维)]
- 总维度：6 + 5 + 24 + 15 = 50维

**边特征构建**：

- 电气参数：[电阻, 电抗, 电纳, 额定容量, 长度]
- 运行状态：[当前潮流, 负载率, 温度, 运行状态]
- PTDF属性：[该线路PTDF系数向量(节点数维), 关键节点影响度(5维)]
- 总维度：9 + PTDF维度

**标签构建**：

- 主要标签：各线路的负载率（连续值）
- 辅助标签：过载标识（0/1二分类）、严重程度等级（1-5分类）

#### 数据预处理
**特征归一化**：

- 数值特征：Z-score标准化，$x_{norm} = \frac{x - \mu}{\sigma}$
- 角度特征：正弦余弦变换，$[\sin(\theta), \cos(\theta)]$
- 类别特征：One-hot编码

**缺失值处理**：
- 时序插值：对连续缺失的时序数据使用线性插值
- 邻居填充：基于图结构用邻居节点均值填充
- 模型预测：使用简单模型预测缺失的PTDF值

**特征工程**：

- 时间特征：小时、星期、月份的周期性编码
- 差分特征：功率变化率、负载率变化率
- 聚合特征：区域总负荷、区域平均负载率

#### 图划分策略
**时间划分**：
- 训练集：前70%时间段数据（约2.1年）
- 验证集：中间15%时间段数据（约0.45年）
- 测试集：最后15%时间段数据（约0.45年）

### 模型设计

#### GNN类型选择：GAT（Graph Attention Network）
**选择理由**：
- 注意力机制能够自动学习节点间的重要性权重
- 适合处理电网中节点重要性差异较大的场景
- 可解释性强，注意力权重可以解释节点间的影响关系
- 对图结构变化（如线路检修）具有较好的鲁棒性

#### 输入输出定义
**输入**：
- 节点特征矩阵：$X \in \mathbb{R}^{N \times 50}$
- 边特征矩阵：$E \in \mathbb{R}^{M \times (9+N+5)}$
- 邻接矩阵：$A \in \{0,1\}^{N \times N}$

**输出**：

- 主要输出：线路负载率预测 $\hat{y} \in [0,1]^M$
- 辅助输出：过载概率 $p_{overload} \in [0,1]^M$

**任务类型**：边级回归任务（预测每条边的负载率）

#### 模型结构设计
**特征编码层**：

- 节点编码器：50维 → 128维，使用LayerNorm + ReLU
- 边编码器：(9+N+5)维 → 64维，使用LayerNorm + ReLU
- 全局编码器：系统级特征 → 32维全局上下文

**图注意力层**：

- 第1层GAT：128维 → 128维，8个注意力头，Dropout=0.1
- 第2层GAT：128维 → 128维，6个注意力头，Dropout=0.1
- 第3层GAT：128维 → 64维，4个注意力头，Dropout=0.1
- 残差连接：每层都添加残差连接和LayerNorm

**聚合方式**：
- 多头注意力聚合：$h_i^{(l+1)} = \|_{k=1}^K \sigma(\sum_{j \in N(i)} \alpha_{ij}^k W^k h_j^{(l)})$
- 边特征更新：$e_{ij}^{(l+1)} = MLP([h_i^{(l+1)}, h_j^{(l+1)}, e_{ij}^{(l)}])$

**输出层设计**：

- 边特征聚合：拼接两端节点特征 + 原始边特征
- 预测网络：(64+64+64) → 64 → 32 → 1，使用ReLU + Sigmoid
- 辅助任务头：共享特征 → 过载分类（Sigmoid激活）

#### 损失函数选择
**主损失函数**：
$$L_{main} = \frac{1}{M} \sum_{i=1}^{M} (y_{pred,i} - y_{true,i})^2$$

**加权损失函数**：
$$L_{weighted} = \frac{1}{M} \sum_{i=1}^{M} w_i (y_{pred,i} - y_{true,i})^2$$
其中 $w_i = 1 + 2 \cdot \mathbb{1}_{y_{true,i} > 0.95}$（过载线路权重更高）

**物理约束损失**：
$$L_{physics} = \frac{1}{N \times M} \sum_{i=1}^{N} \sum_{j=1}^{M} (PTDF_{pred,ij} - PTDF_{true,ij})^2$$

**辅助任务损失**：
$$L_{aux} = -\frac{1}{M} \sum_{i=1}^{M} [y_{overload,i} \log p_{overload,i} + (1-y_{overload,i}) \log(1-p_{overload,i})]$$

**总损失函数**：
$$L_{total} = L_{weighted} + \lambda_1 L_{physics} + \lambda_2 L_{aux}$$
其中 $\lambda_1 = 0.1, \lambda_2 = 0.5$

### 训练配置

#### 优化器选择：AdamW
**参数设置**：
- 学习率：$lr = 1 \times 10^{-4}$
- 权重衰减：$weight\_decay = 1 \times 10^{-5}$
- Beta参数：$\beta_1 = 0.9, \beta_2 = 0.999$
- Epsilon：$\epsilon = 1 \times 10^{-8}$

#### 学习率调度策略
**余弦退火调度**：
$$lr_t = lr_{min} + \frac{1}{2}(lr_{max} - lr_{min})(1 + \cos(\frac{t}{T_{max}} \pi))$$
- 最大学习率：$lr_{max} = 1 \times 10^{-4}$
- 最小学习率：$lr_{min} = 1 \times 10^{-6}$
- 重启周期：$T_{max} = 100$ epochs

**预热策略**：
- 前10个epoch线性增长到最大学习率
- 避免训练初期梯度爆炸

#### 正则化方法
**Dropout**：
- GAT层：Dropout率 = 0.1
- 全连接层：Dropout率 = 0.2
- 注意力权重：Dropout率 = 0.1

**LayerNorm**：

- 每个GAT层后添加LayerNorm
- 稳定训练过程，加速收敛

**权重衰减**：
- L2正则化系数：$1 \times 10^{-5}$
- 防止过拟合

**梯度裁剪**：
- 最大梯度范数：0.5
- 防止梯度爆炸

#### 超参数设置
**批处理设置**：
- 批大小：32个子图
- 子图大小：平均50个节点
- 邻居采样：每层采样15个邻居

**训练轮数**：
- 总训练轮数：500 epochs
- 早停耐心：50 epochs
- 验证频率：每5个epoch

**采样策略**：
- 负采样比例：1:1（正常:过载）
- 时间窗口：连续24小时数据
- 数据增强：添加5%高斯噪声

### 训练流程

#### 前向传播
**步骤1：特征编码**
- 节点特征编码：将50维原始节点特征通过节点编码器映射为128维统一表示
- 边特征编码：将包含PTDF信息的边特征通过边编码器映射为64维表示
- 全局特征编码：将系统级特征编码为32维全局上下文向量

**步骤2：图卷积计算**
- 对每个GAT层依次进行前向传播
- 计算多头注意力权重，学习节点间的重要性关系
- 基于注意力权重聚合邻居节点信息，更新节点特征表示
- 应用残差连接和层归一化，保持训练稳定性和梯度流动

**步骤3：边特征聚合**
- 拼接每条线路两端节点的最终嵌入表示和原始边特征
- 通过预测头网络处理聚合后的边特征
- 输出每条线路的负载率预测值，范围在0到1之间

#### 损失计算
**步骤1：主任务损失**
- 计算负载率预测的均方误差损失
- 对过载线路（负载率>95%）应用更高权重，提升过载预测精度
- 确保模型重点关注安全关键的高负载场景

**步骤2：物理约束损失**
- 基于预测的节点状态计算PTDF矩阵
- 与真实PTDF矩阵对比，计算物理一致性误差
- 确保预测结果符合电力系统潮流分布的物理规律

**步骤3：辅助任务损失**
- 计算过载检测的二分类交叉熵损失
- 辅助主任务学习，提升模型对过载场景的敏感性
- 增强模型的多任务学习能力

**步骤4：总损失**
- 将主任务损失、物理约束损失和辅助任务损失按权重组合
- 权重系数分别为1.0、0.1、0.5，平衡不同目标的重要性

#### 反向传播与参数更新
**步骤1：梯度计算**
- 对总损失函数进行反向传播，计算所有网络参数的梯度
- 梯度从输出层逐层传播到输入层，更新所有可训练参数

**步骤2：梯度裁剪**
- 应用梯度范数裁剪，最大范数设为0.5
- 防止梯度爆炸，保持训练过程稳定

**步骤3：参数更新**
- 使用AdamW优化器根据计算的梯度更新网络参数
- 清零梯度缓存，为下一次迭代做准备

**步骤4：学习率调度**
- 根据余弦退火调度策略更新学习率
- 在训练过程中动态调整学习率，提升收敛效果

#### 验证集评估与早停
**验证指标计算**：
- 计算验证集上的RMSE、MAE、MAPE等回归指标
- 计算过载检测的准确率、精确率、召回率等分类指标
- 评估模型在不同负载水平下的预测性能

**早停机制**：
- 监控验证集RMSE，当连续50个epoch无改进时停止训练
- 保存验证性能最佳的模型检查点
- 防止过拟合，提升模型泛化能力

### 模型评估

#### 测试集评估指标
**回归任务指标**：
- **RMSE**：$RMSE = \sqrt{\frac{1}{M} \sum_{i=1}^{M} (y_{pred,i} - y_{true,i})^2}$
- **MAE**：$MAE = \frac{1}{M} \sum_{i=1}^{M} |y_{pred,i} - y_{true,i}|$
- **MAPE**：$MAPE = \frac{1}{M} \sum_{i=1}^{M} \frac{|y_{pred,i} - y_{true,i}|}{y_{true,i}} \times 100\%$
- **R²决定系数**：$R^2 = 1 - \frac{\sum_{i=1}^{M} (y_{pred,i} - y_{true,i})^2}{\sum_{i=1}^{M} (y_{true,i} - \bar{y})^2}$

**分类任务指标**（过载检测）：
- **准确率**：$Accuracy = \frac{TP + TN}{TP + TN + FP + FN}$
- **精确率**：$Precision = \frac{TP}{TP + FP}$
- **召回率**：$Recall = \frac{TP}{TP + FN}$
- **F1分数**：$F1 = \frac{2 \times Precision \times Recall}{Precision + Recall}$
- **AUC-ROC**：接收者操作特征曲线下面积

**物理一致性指标**：
- **PTDF误差**：$Error_{PTDF} = \frac{1}{N \times M} \sum_{i,j} |PTDF_{pred,ij} - PTDF_{true,ij}|$
- **功率平衡误差**：$Error_{balance} = |\sum P_{gen} - \sum P_{load} - P_{loss}|$

#### 消融实验设计
**特征消融实验**：
- 基线模型：仅使用基础电气特征
- +位置编码：添加多层次位置编码
- +PTDF特征：添加PTDF相关特征
- +时间特征：添加时间周期性特征
- 完整模型：使用所有特征

**结构消融实验**：
- 不同层数：1层、2层、3层、4层GAT
- 不同注意力头数：2、4、6、8个头
- 不同隐藏维度：64、128、256维
- 不同聚合方式：mean、max、attention聚合

**损失函数消融实验**：
- 仅MSE损失
- MSE + 加权损失
- MSE + 物理约束损失
- MSE + 辅助任务损失
- 完整损失函数

#### GPU加速优化
**计算优化**：
- 稀疏矩阵运算：利用图的稀疏性加速计算
- 混合精度训练：使用FP16减少内存占用
- 算子融合：合并相邻算子减少内存访问

**内存优化**：
- 梯度累积：小批量累积梯度模拟大批量训练
- 动态图：按需构建计算图减少内存占用
- 内存池：复用内存减少分配开销

### 模型保存

**检查点保存**：

- 保存模型状态字典，包含所有网络层的权重和偏置参数
- 保存优化器状态，包含动量和学习率等优化器内部状态
- 保存学习率调度器状态，确保训练恢复时调度策略连续
- 记录训练轮数、最佳验证指标、超参数配置和特征缩放器
- 使用统一的检查点格式，便于模型版本管理和部署

## Actor网络设计

### 网络架构

#### 共享GNN Backbone
- **复用预训练GNN**：使用负载率预测网络的前三层GAT作为特征提取器
- **特征维度**：输出64维节点嵌入和边嵌入
- **参数共享策略**：预训练参数作为初始化，允许微调优化

#### 策略生成网络
- **状态聚合层**：将节点和边嵌入聚合为全局状态表示
  - 节点聚合：基于重要性加权平均
  - 边聚合：基于负载率加权平均
  - 全局状态：128维向量
- **策略编码层**：
  - 第1层：128 → 256维，ReLU激活
  - 第2层：256 → 128维，ReLU激活
- **动作输出层**：
  - 连续动作头：输出发电机功率调整的均值和标准差
  - 离散动作头：输出开关操作的概率分布

### 动作空间设计

#### 连续动作空间
- **有功功率调整**：各发电机的出力调整量（MW）
- **无功功率调整**：各发电机的无功调整量（Mvar）
- **动作约束**：考虑发电机爬坡速率和出力限制

#### 离散动作空间
- **线路开关操作**：断路器的开合操作
- **变压器分接头调节**：电压调节操作
- **电容器投切**：无功补偿设备操作

### 策略优化目标

#### 安全性目标
- **过载消除**：将所有线路负载率控制在安全范围内
- **电压稳定**：维持节点电压在允许范围内
- **N-1安全**：确保单一设备故障后系统仍安全

#### 经济性目标
- **调度成本最小化**：最小化发电机调整成本
- **网损最小化**：优化潮流分布减少传输损耗
- **可再生能源利用最大化**：优先利用清洁能源

## Critic网络设计

### 网络架构

#### 状态-动作融合
- **状态输入**：来自Actor的128维全局状态表示
- **动作输入**：Actor输出的调度动作向量
- **融合方式**：早期融合，将状态和动作拼接后处理

#### 价值评估网络
- **融合层**：(128 + 动作维度) → 256维，ReLU激活
- **编码层1**：256 → 256维，ReLU激活
- **编码层2**：256 → 128维，ReLU激活
- **输出层**：128 → 1维，输出Q值

### 价值函数设计

#### 即时奖励函数
$$R_t = w_1 R_{safety} + w_2 R_{economic} + w_3 R_{stability}$$

其中：
- $R_{safety} = -\sum_{i} \max(0, LR_i - 0.95)^2$：安全性奖励
- $R_{economic} = -\sum_{j} C_j |\Delta P_j|$：经济性奖励
- $R_{stability} = -\sum_{k} |V_k - V_{ref,k}|$：稳定性奖励

#### 长期价值评估
- **折扣因子**：$\gamma = 0.99$
- **价值函数**：$V(s) = \mathbb{E}[\sum_{t=0}^{\infty} \gamma^t R_{t+1} | s_t = s]$
- **Q函数**：$Q(s,a) = \mathbb{E}[R_{t+1} + \gamma V(s_{t+1}) | s_t = s, a_t = a]$
