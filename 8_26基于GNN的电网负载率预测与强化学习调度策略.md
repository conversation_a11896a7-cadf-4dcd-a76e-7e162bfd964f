# 基于GNN的电网负载率预测方法

## 建模总体思路

### 问题定义与建模目标

#### 问题陈述
电网负载率预测是电力系统调度运行的核心问题。传统预测方法存在以下挑战：
- **空间相关性建模不足**：忽略了电网节点间复杂的电气耦合关系
- **物理约束缺失**：纯数据驱动方法缺乏电力系统物理规律约束
- **多尺度特征融合困难**：地理、电气、拓扑等多维信息难以有效整合
- **安全性与经济性平衡**：难以在预测精度和运行安全间找到最优平衡点

#### 建模目标
本方案旨在构建一个融合多层次位置信息和物理约束的图神经网络，实现：
1. **高精度预测**：RMSE相比传统方法降低15-20%
2. **物理一致性**：预测结果符合PTDF等电力系统物理约束
4. **安全性导向**：重点关注过载等安全关键场景的预测准确性

### 总体建模架构

```
数据层：     电网运行数据 + 拓扑结构 + 地理信息 + 气象数据
             ↓
特征层：     多层次位置编码 + PTDF物理特征 + 时序特征
             ↓
模型层：     GAT图神经网络 + 复合损失函数 + 注意力机制
             ↓
输出层：     负载率预测 + 过载概率 + 注意力权重解释
             ↓
应用层：     实时监控 + 风险预警 + 调度决策支持
```

### 核心创新

**创新1：多维空间位置编码**

> 将地理位置、电气距离、拓扑结构三维空间信息统一建模，为GNN提供丰富先验知识。

**创新2：PTDF物理约束融入**  

> 将电力系统专业知识嵌入神经网络，确保预测结果符合物理规律，提升模型可信度。

**创新3：安全导向复合损失**

> 对过载等安全关键场景给予更高权重，体现电力系统安全优先原则。

## 建模流程设计

### 数据准备与预处理流程

#### 数据收集策略
```
数据类型          时间跨度    采样频率    主要用途
电网运行数据      3年        15分钟      负载率预测
输入
电网拓扑数据      实时更新    事件驱动    图结构构建
地理位置数据      静态        一次性      地理位置编码
设备参数数据      静态        定期更新    PTDF计算
```

#### 数据质量控制流程
1. **缺失值处理**
   - 时序插值：对连续缺失用线性/样条插值
   - 空间插值：基于电气距离的邻居节点加权插值
   - 模型插值：用简单预测模型填补复杂缺失模式

2. **异常值检测**
   - 统计检测：3σ准则识别统计异常
   - 物理检测：功率平衡等物理约束检验
   - 专家规则：基于电力系统专业知识的规则过滤

3. **数据一致性校验**
   - 时间同步：统一所有数据源的时间戳
   - 单位标准化：统一功率、电压等物理量单位
   - 拓扑一致性：确保网络结构数据的完整性

### 特征工程设计思路

#### 特征分层架构
```
基础特征层 (50维)
├── 静态特征 (15维): 节点类型、容量、电压等级等
├── 动态特征 (20维): 实时电压、功率、负载率等
├── 环境特征 (8维):  温度、湿度、风速等气象因子
└── 时间特征 (7维):  小时、星期、月份等周期性编码

位置编码层 (36维)
├── 地理位置编码 (16维): 多频率正弦余弦变换
├── 电气距离编码 (12维): 基于阻抗矩阵的距离嵌入
└── 拓扑位置编码 (8维):  多种中心性指标组合

PTDF约束层 (15维)
├── 节点PTDF特征 (10维): 对关键线路的影响系数
└── 区域PTDF特征 (5维):  区域间功率传输影响度
```

### 输入特征设计

#### 多层次位置编码（核心创新点）

**设计理念与动机**：
电网系统中节点的"位置"概念远比传统图神经网络复杂，它不仅包括地理空间位置，更重要的是电气空间位置和网络拓扑位置。传统GNN方法仅考虑连接关系，忽略了位置信息对电力传输的重要影响。本方案创新性地设计了三层次位置编码体系：

##### **1. 绝对位置编码（Geographic Positional Encoding）**

**数学定义**：
$$PE_{geo,2i} = \sin(\frac{lat}{10000^{2i/d_{geo}}}) \oplus \sin(\frac{lon}{10000^{2i/d_{geo}}})$$
$$PE_{geo,2i+1} = \cos(\frac{lat}{10000^{2i/d_{geo}}}) \oplus \cos(\frac{lon}{10000^{2i/d_{geo}}})$$

其中：
- $lat, lon$：节点的纬度和经度坐标
- $d_{geo} = 16$：地理位置编码维度
- $\oplus$：特征拼接操作

**设计原理**：

- **周期性表示**：正弦余弦函数能够表示地理坐标的周期性特征
- **尺度不变性**：不同频率的正弦余弦组合能够捕捉不同尺度的地理关系
- **连续性保证**：相邻地理位置的编码向量相似，远距离位置差异明显

**为什么这样设计**：

```
传统方法问题：
- 直接使用经纬度：数值范围差异大，难以学习
- 简单归一化：丢失了地理位置的周期性特征
- 离散化编码：损失了位置的连续性信息

本方案优势：
- 周期性编码：捕捉地理位置的自然周期性
- 多尺度表示：不同频率捕捉局部和全局地理关系
- 数值稳定：输出范围固定在[-1,1]，训练稳定

```

![image-20250821162602401](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250821162602401.png)

##### **2. 相对位置编码（Electrical Distance Encoding）**

**数学定义**：
$$PE_{elec,ij} = \text{Embed}(d_{elec}(i,j))$$
$$d_{elec}(i,j) = \sqrt{(R_{ij})^2 + (X_{ij})^2}$$

其中：
- $d_{elec}(i,j)$：节点i和j之间的电气距离
- $R_{ij}, X_{ij}$：节点间的等效电阻和电抗
- $\text{Embed}(\cdot)$：可学习的距离嵌入函数

**计算流程**：

```
1. 电气距离计算：
   - 基于网络拓扑计算任意两节点间的最短电气路径
   - 考虑线路电阻、电抗、变压器参数
   - 形成N×N的电气距离矩阵

2. 距离分桶编码：
   - 将连续的电气距离离散化为K个区间
   - 每个区间对应一个可学习的嵌入向量
   - 使用softmax加权组合相邻区间的嵌入

3. 相对位置特征：
   - 对每个节点，计算其与所有其他节点的相对位置编码
   - 通过注意力机制聚合为该节点的相对位置特征
   - 输出12维的相对位置编码向量
```

**为什么需要电气距离**：
- **物理意义**：电气距离直接影响功率传输和电压降落
- **传输损耗**：电气距离越大，传输损耗越高
- **电压影响**：电气距离决定了节点间的电压耦合强度
- **故障传播**：电气距离影响故障在网络中的传播路径

##### **拓扑位置编码（Topological Positional Encoding）**

**数学定义**：
基于图的结构特征计算拓扑位置编码：

$$PE_{topo} = [BC(i), CC(i), EC(i), PR(i), CL(i), SP(i)]$$

其中：
- $BC(i)$：节点i的介数中心性（Betweenness Centrality）
- $CC(i)$：节点i的紧密中心性（Closeness Centrality）
- $EC(i)$：节点i的特征向量中心性（Eigenvector Centrality）
- $PR(i)$：节点i的PageRank值
- $CL(i)$：节点i的聚类系数（Clustering Coefficient）
- $SP(i)$：节点i的最短路径长度统计

**详细计算方法**：
```
1. 介数中心性计算：
   BC(i) = Σ(σ_st(i)/σ_st)
   其中σ_st是节点s到t的最短路径数，σ_st(i)是经过节点i的最短路径数

2. 紧密中心性计算：
   CC(i) = (N-1) / Σ_j d(i,j)
   其中d(i,j)是节点i到j的最短路径长度

3. 特征向量中心性：
   通过幂迭代法计算邻接矩阵的主特征向量

4. PageRank计算：
   PR(i) = (1-α)/N + α Σ_j (A_ji * PR(j) / deg(j))
   其中α=0.85是阻尼系数

5. 聚类系数：
   CL(i) = 2*E_i / (deg(i)*(deg(i)-1))
   其中E_i是节点i邻居间的边数
```

**为什么需要拓扑位置编码**：

- **枢纽节点识别**：高介数中心性的节点是电力传输的关键枢纽
- **影响力评估**：PageRank值反映节点在电网中的重要程度
- **局部结构**：聚类系数反映节点的局部连接密度
- **全局位置**：紧密中心性反映节点在全网中的中心程度

#### **多层次位置编码的融合策略**

**编码维度分配**：
```
总位置编码维度：36维
├── 绝对位置编码：16维
│   ├── 纬度编码：8维 (4个频率×2个函数)
│   └── 经度编码：8维 (4个频率×2个函数)
├── 相对位置编码：12维
│   ├── 电气距离嵌入：8维
│   └── 距离统计特征：4维 (最小/最大/平均/方差)
└── 拓扑位置编码：8维
    ├── 中心性指标：6维 (BC/CC/EC/PR/CL/SP各1维)
    └── 度数特征：2维 (入度/出度)
```

**融合方法**：
$$PE_{final} = \text{LayerNorm}(W_1 PE_{geo} + W_2 PE_{elec} + W_3 PE_{topo} + b)$$

其中$W_1, W_2, W_3$是可学习的权重矩阵，$b$是偏置向量。

#### **为什么要建立这样的位置编码体系**

##### **电力系统的空间复杂性**
```
电网空间特性的多维性：
┌─────────────────┬──────────────────┬─────────────────────┐
│   空间维度      │      物理含义     │      影响因素       │
├─────────────────┼──────────────────┼─────────────────────┤
│ 地理空间   │ 物理距离、环境  │ 气象、地形、负荷分布 │
│ 电气空间   │ 电气耦合强度    │ 阻抗、电压、功率流   │
│ 拓扑空间   │ 网络结构重要性  │ 连接度、中心性、冗余 │
└─────────────────┴──────────────────┴────────────┘
```

**单一位置编码的局限性**：
- **仅地理位置**：无法反映电气耦合关系，两个地理相近的节点可能电气距离很远
- **仅拓扑结构**：忽略了物理距离对传输损耗和延时的影响
- **仅电气距离**：无法体现节点在网络中的结构重要性

##### **负载率预测的位置依赖性**

**地理位置的影响**：

- **气象相关性**：相邻地理位置的气象条件相似，影响线路载流量
- **负荷模式**：地理位置决定了负荷类型（工业区、居民区、商业区）
- **发电资源**：地理位置影响可再生能源的出力特性

**电气位置的影响**：
- **潮流分布**：电气距离决定了功率传输的路径选择
- **电压影响**：电气距离影响电压降落和无功功率需求
- **故障传播**：电气距离影响故障对其他节点的影响程度

**拓扑位置的影响**：
- **关键节点**：高中心性节点的故障会对全网产生重大影响
- **冗余路径**：拓扑位置决定了备用传输路径的可用性
- **负荷分配**：拓扑结构影响负荷在网络中的分配模式

##### **传统方法的不足与本方案的创新**

**传统GNN方法的问题**：

```
问题1：位置信息缺失
- 传统GNN只考虑邻接关系，忽略节点的绝对和相对位置
- 无法区分具有相同连接模式但位置不同的节点
- 对于大规模电网，局部结构相似但全局位置不同的区域难以区分

问题2：物理约束缺失
- 纯数据驱动的方法缺乏电力系统的物理约束
- 预测结果可能违反基本的物理定律
- 泛化能力差，在未见过的运行场景下性能下降

问题3：尺度敏感性
- 对电网规模变化敏感，小网络训练的模型难以应用到大网络
- 缺乏尺度不变的特征表示
```

**本方案的创新解决方案**：
```
创新1：多尺度地理编码
- 使用多个频率的正弦余弦函数捕捉不同尺度的地理关系
- 低频分量捕捉大尺度的区域关系
- 高频分量捕捉精细的局部地理特征
- 实现了地理位置的多尺度表示

创新2：电气距离量化
- 将抽象的电气耦合关系量化为具体的距离度量
- 基于电力系统的物理参数（电阻、电抗）计算
- 反映了功率传输的真实物理路径
- 提供了比拓扑距离更准确的节点关系度量

创新3：结构重要性编码
- 通过多种中心性指标全面评估节点的拓扑重要性
- 不同中心性指标捕捉不同类型的结构特征
- 为GNN提供了丰富的结构先验知识
- 提升了模型对关键节点的识别能力
```

#### **位置编码的协同效应**

**三种编码的互补性**：

1. **地理-电气协同**：地理相近但电气距离远的节点（如跨江线路）需要特殊处理
2. **电气-拓扑协同**：电气距离近但拓扑重要性不同的节点有不同的调度优先级
3. **地理-拓扑协同**：地理位置偏远但拓扑重要的节点（如边远重要负荷中心）

**编码融合的学习策略**：

```
自适应权重学习：
- 初始权重：W1=0.4, W2=0.4, W3=0.2 (基于专家经验)
- 训练过程：通过反向传播自动学习最优权重组合
- 任务导向：不同任务（预测vs调度）可能需要不同的权重组合
- 场景自适应：不同运行场景下权重可以动态调整
```

#### **位置编码的学习算法流程**

##### **1. 绝对位置编码（地理位置）流程**

**数据准备阶段**：

```
算法步骤：
1. 收集所有节点的经纬度坐标(lat, lon)
2. 坐标数据清洗：去除异常值，补充缺失值
3. 坐标范围标准化：转换为第一象限到第四象限范围
4. 频率参数设置：确定编码维度和频率数量
```

**编码生成流程**：
```
对于每个节点i：
  获取其经纬度坐标(lat_i, lon_i)
  
  对于频率集合 f = [f_0, f_1, ..., f_k]：
    # 纬度编码
    PE_lat[2j] = sin(lat_i / 10000^(2j/d_geo))
    PE_lat[2j+1] = cos(lat_i / 10000^(2j/d_geo))
    
    # 经度编码
    PE_lon[2j] = sin(lon_i / 10000^(2j/d_geo))
    PE_lon[2j+1] = cos(lon_i / 10000^(2j/d_geo))
  
  # 拼接纬度和经度编码
  PE_geo[i] = concat(PE_lat, PE_lon)
```

**关键特性**：
- 绝对位置编码是**确定性的**，不需要训练学习
- 通过数学变换直接生成，计算效率高
- 不同频率捕捉不同尺度的地理关系

##### **2. 相对位置编码（电气距离）学习流程**

**电气距离计算**：
```
算法步骤：
1. 构建电网阻抗矩阵Z
   对于每条线路k：
     Z[from[k], to[k]] = R[k] + j*X[k]
     Z[to[k], from[k]] = R[k] + j*X[k]

2. 计算节点间电气距离矩阵D_elec
   使用最短路径算法(Floyd-Warshall或Dijkstra)：
   对于每对节点(i,j)：
     D_elec[i,j] = 计算从节点i到j的最小阻抗路径
```

**距离分桶策略**（关键创新点）：

> **什么是距离分桶**：距离分桶是将连续的电气距离值离散化为有限个区间（桶），每个桶对应一个可学习的嵌入向量。这样做的好处是：相似距离的节点对共享相似的表示，使模型能够有效学习距离与负载率之间的关系模式。

```
分桶策略：
1. 确定分桶数量K（通常K=10-20）
   - K太小：无法精细区分不同距离的影响
   - K太大：参数过多，可能过拟合

2. 设计非均匀分桶边界：
   - 近距离区间划分更细：[0, 0.01, 0.02, 0.05, 0.1]
   - 中距离区间适中：[0.1, 0.2, 0.5, 1.0, 2.0]
   - 远距离区间较宽：[2.0, 5.0, 10.0, ∞]

3. 初始化每个桶的嵌入向量：
   对于每个桶k：
     E_dist[k] = 随机初始化(0, 0.1), 维度=12
```

**学习过程**：
```
对于每个训练批次：
  # 1. 前向传播计算编码
  对于每对节点(i,j)：
    获取电气距离 d = D_elec[i,j]
    
    # 找到最接近的桶
    bucket_id = find_bucket(d)
    
    # 计算相邻桶的权重（边界平滑）
    如果 d 在桶边界附近：
      weight_k = softmax(基于距离的相似度计算)
      PE_elec[i,j] = Σ weight_k * E_dist[k]
    否则：
      PE_elec[i,j] = E_dist[bucket_id]
  
  # 2. 整合到GNN计算
  将PE_elec融入节点特征或边特征
  执行GAT网络前向传播
  计算预测损失
  
  # 3. 反向传播更新嵌入
  计算损失对E_dist的梯度
  使用AdamW优化器更新E_dist参数
```

**距离分桶的核心原理**：

距离分桶将连续的电气距离转换为离散的可学习表示，这种做法有以下优势：

1. **参数数量可控**：只需学习K个嵌入向量，而不是N²个（N为节点数）
2. **泛化能力强**：相似距离的节点对共享表示，提高模型泛化性
3. **计算效率高**：避免了为每个节点对单独学习表示
4. **边界平滑**：通过加权组合处理边界情况，避免不连续性

##### **3. 拓扑位置编码学习流程**

**特征提取阶段**：

```
算法步骤：
1. 计算图论中心性指标：
   - 介数中心性：计算经过该节点的最短路径数量
   - 紧密中心性：计算到其他所有节点的平均距离的倒数
   - 特征向量中心性：计算邻接矩阵的主特征向量
   - PageRank值：通过迭代算法计算节点重要性
   - 聚类系数：计算节点邻居间的连接密度

2. 特征标准化：
   对每个中心性指标进行Z-score标准化
   避免不同指标量级差异的影响

3. 拼接原始拓扑特征：
   f_topo[i] = [BC(i), CC(i), EC(i), PR(i), CL(i), degree(i)]
```

**可学习投影阶段**：
```
算法步骤：
1. 初始化投影矩阵：
   W_topo = 随机初始化(6, 8), bias = 随机初始化(8)

2. 前向传播计算：
   对于每个节点i：
     PE_topo[i] = ReLU(W_topo * f_topo[i] + bias)

3. 在训练过程中：
   W_topo和bias作为可学习参数随整个网络一起优化
   目标是学习到最能提升负载率预测精度的拓扑表示
```

##### **4. 位置编码的整合学习算法**

**自适应融合机制**：

```
算法：多维位置编码融合学习

初始化阶段：
1. 初始化融合权重：
   W1 = 0.4  # 地理位置权重
   W2 = 0.4  # 电气距离权重  
   W3 = 0.2  # 拓扑位置权重
   或者使用可学习的权重矩阵

2. 初始化层归一化参数：
   LayerNorm的scale和shift参数

训练过程：
对于每个训练批次：
  # 1. 生成三种编码
  对于每个节点i：
    PE_geo[i] = 计算地理位置编码(确定性)
    PE_elec[i] = 聚合与其他节点的电气距离编码
    PE_topo[i] = W_topo * f_topo[i] + bias
  
  # 2. 加权融合
  对于每个节点i：
    PE_combined[i] = W1*PE_geo[i] + W2*PE_elec[i] + W3*PE_topo[i]
    PE_final[i] = LayerNorm(PE_combined[i])
  
  # 3. 融入GNN计算
  将PE_final与原始节点特征拼接
  执行GAT网络前向传播
  计算预测损失
  
  # 4. 反向传播更新
  计算损失对各参数的梯度：
  - 更新电气距离嵌入表E_dist
  - 更新拓扑投影矩阵W_topo和bias
  - 更新融合权重W1, W2, W3（如果可学习）
  - 更新LayerNorm参数
  - 更新其他GNN参数
```

**学习目标与优化策略**：
- **目标**：学习能最大化负载率预测精度的位置表示
- **约束**：保持物理一致性，避免过拟合
- **正则化**：使用权重衰减和注意力正则化防止过拟合

##### **5. 位置编码训练的完整算法流程**

```
算法：位置编码增强GNN训练

输入：
- G = (V, E)：电网拓扑图
- 节点特征X ∈ R^{N×d_node}
- 边特征E_feat ∈ R^{M×d_edge}
- 节点地理坐标G_coords
- 电气参数P_params

预处理阶段：
1. 计算电气距离矩阵D_elec
2. 计算拓扑中心性指标f_topo
3. 绝对位置编码计算PE_geo（确定性）

参数初始化：
1. 初始化电气距离嵌入表E_dist[K, 12]
2. 初始化拓扑投影矩阵W_topo[6, 8]
3. 初始化GAT网络所有参数
4. 设置优化器AdamW参数

主训练循环：
对于每个训练批次：
  # 步骤1：编码生成
  对于每个节点i ∈ V：
    # 地理位置编码（确定性）
    PE_geo[i] = 正弦余弦变换(lat[i], lon[i])
    
    # 电气距离编码（可学习）
    PE_elec_list = []
    对于每个其他节点j ≠ i：
      d = D_elec[i,j]
      bucket_id, weights = find_bucket_with_weights(d)
      pe = Σ weights[k] * E_dist[k] for k in nearby_buckets
      PE_elec_list.append(pe)
    PE_elec[i] = aggregate(PE_elec_list)  # 聚合为固定维度
    
    # 拓扑位置编码（可学习）
    PE_topo[i] = ReLU(W_topo * f_topo[i] + bias_topo)
  
  # 步骤2：位置编码融合
  对于每个节点i：
    PE_combined[i] = W1*PE_geo[i] + W2*PE_elec[i] + W3*PE_topo[i]
    PE_final[i] = LayerNorm(PE_combined[i])
  
  # 步骤3：GNN前向传播
  节点特征增强：X_enhanced = concat(X, PE_final)
  执行三层GAT网络前向传播
  计算负载率预测结果
  
  # 步骤4：损失计算
  计算主要预测损失
  计算物理约束损失
  计算辅助任务损失
  组合为总损失函数
  
  # 步骤5：反向传播更新
  计算总损失对所有参数的梯度：
  - ∇E_dist：电气距离嵌入表的梯度
  - ∇W_topo：拓扑投影矩阵的梯度
  - ∇W1,W2,W3：融合权重的梯度（如果可学习）
  - ∇Θ_GAT：GAT网络所有参数的梯度
  
  使用AdamW优化器更新所有参数：
  E_dist ← AdamW_update(E_dist, ∇E_dist)
  W_topo ← AdamW_update(W_topo, ∇W_topo)
  Θ_GAT ← AdamW_update(Θ_GAT, ∇Θ_GAT)
```

**关键学习特点**：
1. **端到端学习**：位置编码与主任务一起优化，保证了任务相关性

2. **分层学习**：不同类型的编码使用不同的学习策略

3. **正则化约束**：防止位置编码过拟合，保持泛化能力

4. **自适应调整**：融合权重可以根据任务需求自动调整

   为GNN提供了丰富而准确的位置先验知识，是实现高精度负载率预测的关键技术创新。

#### PTDF物理约束融入（核心创新点）

##### **设计理念与动机**
电力系统中的功率传输遵循严格的物理定律，而纯数据驱动的神经网络容易产生违反这些基本物理原理的预测结果。PTDF（Power Transfer Distribution Factor，功率传输分布因子）是电力系统中描述功率注入与线路潮流关系的核心指标。

> **创新意义**：本方案创新性地将PTDF等电力系统专业知识作为网络特征和约束条件嵌入到GNN中，实现了“知识引导的学习”。这种物理知识与数据驱动的深度融合，不仅提升了预测精度，更重要的是保证了AI模型输出的可靠性和安全性，这对于电网这种对安全性极度敏感的关键基础设施至关重要。

##### **PTDF物理原理**
PTDF矩阵描述了电网中任意节点功率注入改变对各条线路潮流的影响：

$$\Delta F_{line,i} = \sum_{j=1}^{N} PTDF_{i,j} \cdot \Delta P_{inject,j}$$

其中：
- $\Delta F_{line,i}$：线路i潮流变化量
- $PTDF_{i,j}$：节点j对线路i的PTDF系数  
- $\Delta P_{inject,j}$：节点j的功率注入变化量

将PTDF**放在边特征**：

1. PTDF本质是“节点注入对线路的敏感性”，直接与线路负载相关
2. 可以用**行向量统计量**（模长、最大/平均、主要影响节点数） → 固定维度，训练稳定
3. 对节点预测也能间接帮助（GNN消息传递中，边特征参与节点聚合）

- 节点特征保留原始属性（历史负荷、地理信息、容量等）即可
- 全局特征用于功率平衡约束

 间接PTDF统计的概念

- **PTDF本质**：线路 × 节点矩阵，`PTDF[i,j]` 表示节点 j 的功率注入变化对线路 i 潮流变化的敏感性。
- **问题**：每个节点对应的列向量长度 = 线路数（可能远大于100），如果直接放入节点特征会导致维度过大 → 训练不稳定。

**解决方法**：节点特征中只保留 **列向量的统计信息**，称为“间接PTDF统计”。即：对节点 j 的列向量 PTDF[:, j] 进行统计，得到 **几个标量**，表示该节点注入对网络整体的影响情况。

PTDF信息 **不直接放入节点列向量**（否则列维度会等于线路数，非常大），而是通过 **边特征 + 间接统计量** 体现节点对网络敏感性。

**最大PTDF**：节点注入最可能导致哪条线路潮流变化最大

**平均PTDF**：节点整体对网络潮流的平均影响

​      这种物理知识与GNN的深度融合代表了人工智能在电力系统领域应用的重要发展方向，即从纯数据驱动向知识引导的智能化转变。

### 损失函数设计

**设计理念**：PTDF已作为节点特征输入，损失函数专注于负载率预测的准确性和安全性。

#### 1. 主要损失：安全导向负载率预测

$$L_{main} = \frac{1}{M} \sum_{i=1}^{M} w_i (LR_{pred,i} - LR_{true,i})^2$$

权重设计：
$$w_i = \begin{cases}
1.0 & \text{if } LR_{true,i} \leq 0.8 \\
2.0 & \text{if } 0.8 < LR_{true,i} \leq 0.95 \\
4.0 & \text{if } LR_{true,i} > 0.95
\end{cases}$$

**设计原理**：对过载线路给予更高权重，重点关注安全关键场景。

$M$ = 被平均的数量（所有参与损失计算的线路数或样本数）。

#### 2. 载流量约束损失(预测用“无约束”，决策用“有约束”)

$$L_{ampacity} = \frac{1}{M} \sum_{i=1}^{M} \max(0, LR_{pred,i} - \frac{I_{max,i}}{I_{rated,i}})^2$$

**设计原理**：硬约束确保预测负载率不超过实际载流量限制。$I_{max,i}$为考虑环境因素的动态载流量，$I_{rated,i}$为额定电流。
$$
L_{ampacity} = \frac{1}{M} \sum_{i=1}^{M} 
\max\!\Big(0, \, LR_{pred,i} - \frac{I_{max,i}}{I_{rated,i}}\Big)^2
$$

其中：

- $LR_{pred,i}$ ：模型预测的线路负荷率（line loading rate），即 **预测线路电流 / 额定电流**。
- $\tfrac{I_{max,i}}{I_{rated,i}}$ ：考虑环境因素后的 **允许最大负荷率**。
  - $I_{max,i}$ ：线路在当前环境（温度、风速等）下允许的最大电流（动态热稳定限制）。
  - $I_{rated,i}$ ：线路额定电流。
  - 因此 $\tfrac{I_{max,i}}{I_{rated,i}}$ 就是 **线路在实际条件下的安全上限**。
- $\max\!\big(0, LR_{pred,i} - \tfrac{I_{max,i}}{I_{rated,i}}\big)$ ：  
  - 如果预测负荷率 **不超过安全上限** → 结果为 $0$（不惩罚）；  
  - 如果预测负荷率 **超过安全上限** → 取超出的部分作为惩罚项。
- 平方惩罚 $(\cdot)^2$ ：对超限越多的线路惩罚越大，形成 **硬约束效果**。
- $\tfrac{1}{M}\sum_{i=1}^M$ ：对所有线路/样本平均，保持损失与规模无关。

#### 3. 功率平衡约束损失

$$L_{balance} = \left|\sum_{j=1}^{N} P_{gen,j} - \sum_{j=1}^{N} P_{load,j} - P_{loss}\right|^2$$

**设计原理**：基于能量守恒定律，确保系统功率平衡，维持频率稳定。
$$
L_{balance} = \left|\sum_{j=1}^{N} P_{gen,j} - \sum_{j=1}^{N} P_{load,j} - P_{loss}\right|^2
$$

其中：

- $\sum_{j=1}^{N} P_{gen,j}$ ：所有发电机的总有功功率输出。  
- $\sum_{j=1}^{N} P_{load,j}$ ：系统内所有负荷的总有功功率需求。  
- $P_{loss}$ ：系统在传输过程中的有功功率损耗（主要由线路电阻引起）。  

整体含义：  
$$
\text{功率不平衡量} 
= \sum_{j=1}^{N} P_{gen,j} 
- \sum_{j=1}^{N} P_{load,j} 
- P_{loss}
$$
代表 **发电与负荷 + 损耗之间的差值**。  

- 平方惩罚 $|\cdot|^2$ ：  
  - 若功率平衡满足（差值=0），损失为 0；  
  - 若不平衡，损失随差值平方增长，强制模型学习维持功率平衡。  

电力系统必须遵循 **能量守恒定律**：  
$$
\text{总发电功率} = \text{总负荷功率} + \text{系统损耗}
$$
如果预测/调度方案违反功率平衡 → 系统频率会偏离额定值（50Hz/60Hz），威胁稳定性。  

因此该损失函数用于 **保证模型输出满足功率平衡约束，维持系统频率稳定**。  

#### 4. 辅助任务损失

$$L_{aux} = -\frac{1}{M} \sum_{i=1}^{M} [y_{overload,i} \log p_{overload,i} + (1-y_{overload,i}) \log(1-p_{overload,i})]$$

**设计原理**：过载检测辅助任务，提升模型对关键场景的敏感性。

### 工作机制

- 当真实标签 $y_{overload,i} = 1$（过载）：
   损失项 = $-\log p_{overload,i}$
   → 如果预测概率 $p_{overload,i}$ 越接近 $1$，损失越小；预测错误（$p_{overload,i}$ 很小），损失越大。
- 当真实标签 $y_{overload,i} = 0$（未过载）：
   损失项 = $-\log (1-p_{overload,i})$
   → 如果预测概率 $p_{overload,i}$ 越接近 $0$，损失越小；预测错误（$p_{overload,i}$ 很大），损失越大。

所以，整个损失就是 **监督模型学会判断“是否过载”**。

#### 5. 总损失函数

$$L_{total} = L_{main} + 0.3 L_{ampacity} + 0.2 L_{balance} + 0.4 L_{aux}$$

**权重设计**：载流量约束和过载检测权重较高，体现安全优先原则。

📌 **总结**：  

- $L_{main}$ ：保证预测准确性。  
- $L_{ampacity}$ ：保证线路不过载。  
- $L_{balance}$ ：保证整体能量守恒，维持频率稳定。  

**增加过载检测辅助任务**：

- 通过 **二分类的辅助监督信号**，让模型在接近过载阈值时更加敏感。
- 提高对关键场景（过载风险）的识别能力。

## 新增电网物理参数集成方案

### 参数现状分析与集成规划

#### 已使用参数清单
根据现有模型分析，以下电网物理参数已经在模型中得到应用：

1. **电压等级** ✅ - 位于节点静态特征中，用于区分不同电压等级的设备
2. **负荷率** ✅ - 位于节点动态特征和边运行状态中，是核心预测目标
3. **线路名称** ✅ - 通过边ID进行标识，用于线路区分和追踪
4. **序号（编码）** ✅ - 通过节点ID和边ID体现，用于网络拓扑构建

#### 待集成参数清单
需要新增集成的电网物理参数：

5. **线路最大载流量** ❌ - 关键安全参数，需要集成到边特征
6. **变电站特征** ❌ - 需要增强变电站节点的特征表示
7. **最大负荷时间** ❌ - 时间相关特征，反映负荷峰值出现规律
8. **度夏负荷** ❌ - 季节性负荷特征，反映夏季用电高峰特性
9. **线额定电流** ❌ - 线路基础参数，需要添加到边特征
10. **线最大载流量** ❌ - 与第5项相同，线路安全运行的关键指标

### 新增参数的特征工程设计

#### 变电站节点特征增强（新增4维）

**设计理念**：
变电站作为电网的关键枢纽节点，其特征表示需要更加丰富和精确。传统方法往往将变电站简化为普通节点，忽略了其在电网中的特殊作用。

**新增特征定义**：
```
变电站特征向量 = [变电站类型, 变压器容量, 母线数量, 接入线路数]

1. 变电站类型 (1维):
   - 枢纽变电站: 0.9-1.0 (最重要的区域性枢纽)
   - 终端变电站: 0.7-0.8 (直接供电给用户)
   - 联络变电站: 0.5-0.6 (连接不同电压等级)
   - 开关站: 0.3-0.4 (主要起开关作用)

2. 变压器容量 (1维):
   - 归一化处理: 容量值 / 系统最大变压器容量
   - 反映变电站的功率处理能力

3. 母线数量 (1维):
   - 直接使用母线数量，反映变电站的复杂程度
   - 影响潮流分布和故障传播路径

4. 接入线路数 (1维):
   - 连接到该变电站的线路总数
   - 反映变电站在网络中的连接重要性
```

**工程意义**：
- **故障影响评估**：变电站类型和接入线路数直接影响故障传播范围
- **负荷分配优化**：变压器容量决定了负荷分配的上限约束
- **网络拓扑分析**：母线配置影响潮流分布和系统稳定性

#### 时间负荷特征增强（新增4维）

**设计理念**：
电网负荷具有明显的时间周期性和季节性特征，特别是"最大负荷时间"和"度夏负荷"反映了电网运行的关键时间特性。

**新增特征定义**：
```
时间负荷特征向量 = [历史最大负荷时间, 度夏负荷峰值, 季节负荷系数, 负荷增长率]

1. 历史最大负荷时间 (1维):
   - 正弦余弦编码: [sin(2π * hour/24), cos(2π * hour/24)]
   - 反映该节点负荷峰值的典型出现时间
   - 用于预测未来负荷峰值的时间分布

2. 度夏负荷峰值 (1维):
   - 归一化处理: 度夏峰值负荷 / 年度最大负荷
   - 反映夏季空调负荷对该节点的影响程度
   - 范围通常在0.8-1.2之间

3. 季节负荷系数 (1维):
   - 当前季节负荷 / 年平均负荷
   - 春季: 0.8-0.9, 夏季: 1.1-1.3, 秋季: 0.9-1.0, 冬季: 1.0-1.2
   - 反映季节性用电特征

4. 负荷增长率 (1维):
   - 年度负荷增长率，反映该区域的发展趋势
   - 正值表示增长，负值表示下降
   - 影响长期负荷预测和规划决策
```

**工程意义**：
- **峰值预测精度**：最大负荷时间特征提升高峰时段预测准确性
- **季节性建模**：度夏负荷和季节系数捕捉季节性变化规律
- **趋势分析**：负荷增长率支持中长期预测和规划

#### 线路载流量特征增强（新增4维）

**设计理念**：
线路载流量是电网安全运行的核心约束，不同运行条件下的载流量限制直接影响线路负荷分配和安全裕度。

**新增特征定义**：
```
载流量特征向量 = [线路额定电流, 最大载流量, 紧急载流量, 热稳定极限]

1. 线路额定电流 (1维):
   - 标准运行条件下的持续载流量 (A)
   - 归一化处理: 额定电流 / 系统最大线路额定电流
   - 基础安全运行约束

2. 最大载流量 (1维):
   - 考虑环境条件的最大允许电流 (A)
   - 受温度、风速、日照等气象因素影响
   - 动态调整的安全上限

3. 紧急载流量 (1维):
   - 紧急情况下短时间允许的最大电流 (A)
   - 通常为额定电流的1.2-1.5倍
   - 用于故障后的紧急调度

4. 热稳定极限 (1维):
   - 导线热稳定的绝对上限 (A)
   - 超过此值将导致导线损坏
   - 硬约束条件，不可违反
```

**工程意义**：
- **安全约束建模**：多层次载流量限制提供精确的安全约束
- **动态调度支持**：不同载流量限制支持不同运行模式的调度决策
- **风险评估**：载流量裕度计算支持线路过载风险评估

#### 线路安全裕度特征增强（新增4维）

**设计理念**：
除了静态的载流量参数，线路的历史运行状态和安全裕度信息对负荷率预测同样重要。

**新增特征定义**：
```
安全裕度特征向量 = [载流量裕度, 过载历史次数, 最近过载时间, 线路重要度等级]

1. 载流量裕度 (1维):
   - 计算公式: (最大载流量 - 当前电流) / 最大载流量
   - 范围: 0-1，值越大表示裕度越充足
   - 实时反映线路安全状态

2. 过载历史次数 (1维):
   - 过去一年内该线路过载次数
   - 对数变换: log(1 + 过载次数)
   - 反映线路过载倾向性

3. 最近过载时间 (1维):
   - 距离最近一次过载的天数
   - 归一化处理: 天数 / 365
   - 反映线路当前安全状态的稳定性

4. 线路重要度等级 (1维):
   - 基于网络拓扑分析的线路重要性评级
   - 一级重要: 0.9-1.0 (骨干网络)
   - 二级重要: 0.7-0.8 (区域主干)
   - 三级重要: 0.5-0.6 (配送网络)
   - 四级重要: 0.3-0.4 (末端线路)
```

**工程意义**：
- **预防性维护**：过载历史信息支持预防性维护决策
- **风险预警**：载流量裕度提供实时风险预警
- **调度优先级**：线路重要度等级指导调度决策优先级

### 特征集成的技术实现

#### 数据预处理流程

**1. 数据收集与清洗**
```
数据源整合：
├── SCADA系统: 实时运行数据 (载流量、负荷等)
├── EMS系统: 设备参数数据 (额定值、限值等)
├── 历史数据库: 历史负荷和过载记录
└── 气象系统: 环境参数 (影响载流量的因素)

数据质量控制：
├── 缺失值处理: 基于物理约束的插值方法
├── 异常值检测: 基于3σ准则和专家规则
├── 数据一致性: 跨系统数据的时间同步和单位统一
└── 完整性检查: 确保关键参数的完整性
```

**2. 特征工程实现**
```python
# 伪代码示例
def enhance_node_features(original_features, substation_data, load_history):
    """节点特征增强"""
    # 原始特征 (6+5+24+15=50维)
    enhanced_features = original_features.copy()

    # 新增变电站特征 (4维)
    substation_features = [
        substation_data['type_encoding'],      # 变电站类型
        substation_data['transformer_capacity'] / max_capacity,  # 归一化容量
        substation_data['bus_count'],          # 母线数量
        substation_data['connected_lines']     # 接入线路数
    ]

    # 新增时间负荷特征 (4维)
    time_load_features = [
        encode_time_cyclical(load_history['peak_hour']),  # 最大负荷时间
        load_history['summer_peak'] / load_history['annual_max'],  # 度夏负荷
        get_seasonal_coefficient(current_season),  # 季节负荷系数
        load_history['growth_rate']            # 负荷增长率
    ]

    # 拼接所有特征 (50+4+4=58维)
    enhanced_features = np.concatenate([
        enhanced_features,
        substation_features,
        time_load_features
    ])

    return enhanced_features

def enhance_edge_features(original_features, line_data, operation_history):
    """边特征增强"""
    # 原始特征 (5+4+节点数+5维)
    enhanced_features = original_features.copy()

    # 新增载流量特征 (4维)
    ampacity_features = [
        line_data['rated_current'] / max_rated_current,    # 归一化额定电流
        line_data['max_ampacity'] / max_ampacity,          # 归一化最大载流量
        line_data['emergency_ampacity'] / max_emergency,   # 归一化紧急载流量
        line_data['thermal_limit'] / max_thermal           # 归一化热稳定极限
    ]

    # 新增安全裕度特征 (4维)
    safety_features = [
        calculate_ampacity_margin(line_data),              # 载流量裕度
        np.log(1 + operation_history['overload_count']),   # 过载历史次数
        operation_history['days_since_overload'] / 365,    # 最近过载时间
        line_data['importance_level']                      # 线路重要度等级
    ]

    # 拼接所有特征 (原维度+4+4维)
    enhanced_features = np.concatenate([
        enhanced_features,
        ampacity_features,
        safety_features
    ])

    return enhanced_features
```

#### 模型架构适配

**1. 编码器维度调整**
```
节点编码器: 58维 → 128维
├── 输入层: Linear(58, 128)
├── 归一化: LayerNorm(128)
├── 激活函数: ReLU()
└── Dropout: 0.1

边编码器: (17+N+5)维 → 64维
├── 输入层: Linear(17+N+5, 64)
├── 归一化: LayerNorm(64)
├── 激活函数: ReLU()
└── Dropout: 0.1
```

**2. 注意力机制增强**
```
多头注意力分工：
├── 头1-2: 载流量约束关系 (基于新增载流量特征)
├── 头3-4: 变电站枢纽关系 (基于变电站重要性)
├── 头5-6: 时间负荷模式 (基于时间负荷特征)
└── 头7-8: 安全裕度关系 (基于安全裕度特征)
```

### 集成效果预期分析

#### 预测精度提升预期

**1. 载流量特征的贡献**
- **安全约束精确化**：多层次载流量限制提供更精确的安全边界
- **动态调整能力**：考虑环境因素的载流量动态调整
- **预期RMSE改善**：5-8%的精度提升

**2. 变电站特征的贡献**
- **枢纽节点建模**：更准确地建模变电站的枢纽作用
- **故障传播分析**：更好地预测故障对负荷分布的影响
- **预期RMSE改善**：3-5%的精度提升

**3. 时间负荷特征的贡献**
- **峰值预测增强**：显著提升高峰时段的预测准确性
- **季节性建模**：更好地捕捉季节性负荷变化
- **预期RMSE改善**：8-12%的精度提升

#### 模型鲁棒性增强

**1. 异常场景适应性**
- **过载历史信息**：提升对潜在过载线路的识别能力
- **安全裕度监控**：增强对临界安全状态的敏感性
- **紧急调度支持**：为紧急情况下的调度提供更好支持

**2. 长期预测稳定性**
- **负荷增长趋势**：支持中长期负荷预测
- **设备老化建模**：考虑设备参数随时间的变化
- **规划决策支持**：为电网规划提供更可靠的数据基础

## GNN模型架构设计

### GAT网络选择与优化

#### GAT选择理由
选择GAT（Graph Attention Network）作为主干网络的深层原因：

**1. 电网节点异质性高**
电网中包含发电机、负荷、变电站等不同类型的节点，各节点的重要性差异很大。GAT的注意力机制能够自适应地学习节点间的重要性权重。

**2. 电气耦合关系复杂**
电网中的节点关系不仅仅是拓扑连接，还包括电气耦合、地理邻近、功能相似等多种关系。多头注意力机制能够同时捕捉这些不同类型的关系。

**3. 拓扑动态适应性**
电网运行中经常发生设备检修、故障等导致拓扑变化的情况。GAT对图结构变化具有较好的鲁棒性，能够适应动态的网络环境。

**4. 可解释性要求**
电网运行对可解释性要求很高，调度人员需要理解AI决策的依据。GAT的注意力权重可以直观地显示节点间的影响关系，提供了好的可解释性。

#### GAT架构优化设计

**1. 多尺度注意力设计**

```
第1层GAT (8个注意力头): 学习直接邻居关系
│（根据直接数据）
├── 头1-2: 电气耦合关系 (基于阻抗相似性)
├── 头3-4: 地理邻近关系 (基于地理位置编码)
├── 头5-6: 功能相似关系 (基于节点类型)
└── 头7-8: 负荷相关关系 (基于历史负荷模式)

第2层GAT (6个注意力头): 扩展二跳邻居关系
│（根据拓扑关系）
├── 头1-2: 间接电气影响 (通过中间节点的影响)
├── 头3-4: 区域层面关系 (同电压等级区域内节点)
└── 头5-6: 功率传输路径 (基PTDF关系学习)

第3层GAT (4个注意力头): 全局结构信息整合
│（考虑全局）
├── 头1-2: 关键节点识别 (高中心性节点关注)
└── 头3-4: 系统稳定性分析 (全局稳定性相关节点)
```

**2. 注意力计算优化**
传统GAT的注意力计算可能忽略电网特有的物理约束，本方案设计了融合物理信息的注意力机制：

**基础注意力计算**：
$$e_{ij} = a(W_h h_i, W_h h_j)$$

**物理增强注意力**：
$$e_{ij}^{enhanced} = e_{ij} + \beta_1 \cdot PTDF_{ij} + \beta_2 \cdot PE_{elec}(i,j) + \beta_3 \cdot PE_{geo}(i,j)$$

**最终注意力权重**：
$$\alpha_{ij} = \frac{\exp(e_{ij}^{enhanced})}{\sum_{k \in \mathcal{N}(i)} \exp(e_{ik}^{enhanced})}$$

## GNN负载率预测算法

### GAT网络架构设计

#### 网络选择理由
选择GAT（Graph Attention Network）作为主干网络的原因：
- **自适应权重学习**：注意力机制能够自动学习节点间的重要性权重
- **多头注意力机制**：可以捕捉不同类型的节点关系（电气耦合、地理邻近、功能相似）
- **拓扑鲁棒性**：对图结构变化具有较好的适应性，适合电网拓扑的动态变化
- **可解释性强**：注意力权重可以解释节点间的影响关系

#### 网络架构设计
**特征编码层**：
- 节点编码器：48维原始特征 → 128维统一表示
- 边编码器：(17+N+5)维边特征 → 64维表示
- 全局编码器：56维系统特征 → 32维全局上下文

**三层GAT主干网络**：
- **第1层GAT**：128维 → 128维，8个注意力头，学习直接邻居关系
- **第2层GAT**：128维 → 128维，6个注意力头，学习二跳邻居关系
- **第3层GAT**：128维 → 64维，4个注意力头，学习全局结构关系

### 创新损失函数设计

#### 重新设计损失函数的优势分析

**1. 安全导向的分层权重优势**：
- **精准安全关注**：通过分层权重设计，精确关注不同安全等级的线路
- **过载预防**：对临近过载和已过载线路的重点关注，提升安全预警能力
- **运行经验融入**：权重设计体现了电网运行的实际安全管理经验

**2. 物理一致性约束优势**：
- **PTDF知识利用**：虽然不预测PTDF，但通过物理约束确保预测结果符合潮流分布规律
- **数值稳定性**：标准化处理提高了物理约束计算的数值稳定性
- **验证机制**：为预测结果提供了物理层面的验证和矫正

**3. 载流量约束矫正优势**：
- **硬约束实现**：通过max函数实现载流量的硬约束，防止预测结果违反物理限制
- **动态适应**：考虑环境因素的动态载流量，提高预测的实用性
- **安全保障**：从根本上保证预测结果的安全性

**4. 多任务协同优势**：
- **任务互补**：负载率预测和过载检测相互促进，提升整体性能
- **特征共享**：多任务学习促进特征表示的丰富性和泛化能力
- **实用性增强**：同时输出负载率和过载概率，满足不同应用需求

**5. 时间一致性优势**：
- **平滑性保证**：避免预测结果出现不合理的时间跳跃
- **趋势准确性**：确保负载率变化趋势的物理合理性
- **连续性维护**：保持时间序列预测的连续性和一致性

#### 损失函数设计分析

**1. 物理意义明确的优势**：

- **直观可解释**：每项损失都有明确的物理含义，便于理解和分析
- **权重设置合理**：基于电力系统专业知识设置权重比例
- **目标导向清晰**：每项损失直接对应一个具体的优化目标
- **调试便利**：可以单独分析每项损失的贡献，便于问题定位

**2. 多任务平衡的优势**：

- **任务权重平衡**：通过简单的线性组合平衡不同任务的重要性
- **避免任务冲突**：权重系数经过精心设计，避免任务间的负面干扰
- **联合优化效果**：简洁的组合方式使多任务能够协同优化
- **泛化能力强**：简洁的设计提高了模型在不同场景下的适应性

### 训练算法流程

#### 训练流程伪代码
```
算法：GNN负载率预测模型训练
输入：训练数据集D_train，验证数据集D_val，超参数配置H
输出：训练好的GNN模型M

初始化阶段：
1. 创建GAT模型M，包含特征编码器、三层GAT和输出头
2. 创建AdamW优化器，学习率lr=1e-4，权重衰减wd=1e-5
3. 创建余弦退火学习率调度器，周期T=100
4. 设置最佳验证损失best_val_loss为无穷大
5. 设置早停耐心值patience_counter为0，最大耐心值max_patience为50

主训练循环：
对于每个训练轮次epoch从1到max_epochs：
  设置模型为训练模式
  初始化当前轮次训练损失为0

  对于训练数据加载器中的每个批次：
    提取批次数据：节点特征X、边特征E、邻接矩阵A、真实标签Y

    前向传播阶段：
    - 通过节点编码器处理62维节点特征得到128维表示
    - 通过边编码器处理变长边特征得到64维表示
    - 通过全局编码器处理56维系统特征得到32维上下文
    - 第1层GAT：计算8头注意力权重，聚合邻居信息
    - 第2层GAT：计算6头注意力权重，扩展感受野
    - 第3层GAT：计算4头注意力权重，全局信息整合
    - 输出层：生成负载率预测和过载概率

    损失计算阶段：
    - 计算主要损失：安全导向的分层加权负载率损失
    - 计算载流量约束损失：动态载流量限制的硬约束损失
    - 计算功率平衡损失：系统功率平衡约束损失
    - 计算辅助任务损失：过载检测的二分类交叉熵损失
    - 组合总损失：四项损失按权重组合

    反向传播阶段：
    - 计算梯度：对总损失进行反向传播
    - 梯度裁剪：限制梯度范数在0.5以内防止梯度爆炸
    - 参数更新：使用AdamW优化器更新所有参数
    - 清零梯度：为下一次迭代准备

    累积训练损失

  更新学习率调度器

  验证阶段：
  设置模型为评估模式
  计算验证集上的损失和各项指标

  模型保存与早停：
  如果验证损失改善：
    保存当前最佳模型检查点
    重置耐心计数器为0
  否则：
    增加耐心计数器
    如果超过最大耐心值：
      停止训练，加载最佳模型检查点
```
- **历史运行数据**：收集电网3年历史运行数据，包含正常、故障、极端天气等多种场景
- **拓扑数据**：电网节点连接关系、线路参数、设备额定参数
- **气象数据**：温度、湿度、风速等影响线路载流量的环境因素
- **负荷预测数据**：各节点的负荷预测值和实际值
- **发电计划数据**：各发电机的出力计划和实际出力

#### 图数据构建
**节点定义**：
- 发电机节点：火电、水电、风电、光伏等发电设备
- 负荷节点：工业、商业、居民等用电负荷
- 变电站节点：电压变换和潮流汇集点

**边定义**：
- 输电线路：高压交流线路、直流线路
- 变压器：电压等级变换设备
- 开关设备：断路器、隔离开关等

**节点特征构建**：
- 静态特征：[节点类型, 额定容量, 电压等级, 经度, 纬度, 海拔] (6维)
- 动态特征：[有功功率, 无功功率, 电压幅值, 电压相角, 负载率] (5维)
- **新增变电站特征**：[变电站类型, 变压器容量, 母线数量, 接入线路数] (4维)
- **新增时间负荷特征**：[历史最大负荷时间(小时), 度夏负荷峰值, 季节负荷系数, 负荷增长率] (4维)
- 位置编码：[绝对位置编码(8维), 相对位置编码(8维), 拓扑位置编码(8维)] (24维)
- **间接PTDF统计特征**：[最大PTDF系数, 平均PTDF系数, PTDF系数标准差, 主要影响线路数, 影响范围指标] (5维)
- 总维度：6 + 5 + 4 + 4 + 24 + 5 = 48维

#### 间接PTDF统计特征设计详解

**设计理念**：
PTDF矩阵的完整信息维度过高（N×M），直接作为特征会导致维度爆炸和训练不稳定。通过统计方法将每个节点的PTDF列向量压缩为几个关键标量，既保留了重要的物理信息，又控制了特征维度。

**特征定义**：
对于节点j，其PTDF列向量为 $PTDF_{:,j} = [PTDF_{1,j}, PTDF_{2,j}, ..., PTDF_{M,j}]$

```
1. 最大PTDF系数 (1维):
   max_ptdf_j = max(|PTDF_{:,j}|)
   物理意义：该节点功率注入对哪条线路影响最大

2. 平均PTDF系数 (1维):
   mean_ptdf_j = mean(|PTDF_{:,j}|)
   物理意义：该节点对整个网络的平均影响程度

3. PTDF系数标准差 (1维):
   std_ptdf_j = std(PTDF_{:,j})
   物理意义：该节点对不同线路影响的差异程度

4. 主要影响线路数 (1维):
   major_lines_j = count(|PTDF_{i,j}| > threshold)
   物理意义：该节点显著影响的线路数量（threshold=0.05）

5. 影响范围指标 (1维):
   range_index_j = (max_ptdf_j - min_ptdf_j) / mean_ptdf_j
   物理意义：该节点影响的集中度vs分散度
```

**为什么这样设计**：

- **维度可控**：将N×M的PTDF矩阵压缩为每个节点5维特征
- **物理意义明确**：每个统计量都有清晰的电力系统物理解释
- **信息保留**：关键的PTDF分布特征得到保留
- **计算高效**：统计计算复杂度低，适合实时应用

**边特征构建**：

- 电气参数：[电阻, 电抗, 电纳, 额定容量, 长度] (5维)
- **新增载流量特征**：[线路额定电流, 最大载流量, 紧急载流量, 热稳定极限] (4维)
- 运行状态：[当前潮流, 负载率, 温度, 运行状态] (4维)
- **新增安全裕度特征**：[载流量裕度, 过载历史次数, 最近过载时间, 线路重要度等级] (4维)
- PTDF属性：[该线路PTDF系数向量(节点数维), 关键节点影响度(5维)] (节点数+5维)
- 总维度：5 + 4 + 4 + 4 + (节点数+5) = 17 + 节点数维

**标签构建**：

- 主要标签：各线路的负载率（连续值）
- 辅助标签：过载标识（0/1二分类）、严重程度等级（1-5分类）

#### 数据预处理
**特征归一化**：

- 数值特征：Z-score标准化，$x_{norm} = \frac{x - \mu}{\sigma}$
- 角度特征：正弦余弦变换，$[\sin(\theta), \cos(\theta)]$
- 类别特征：One-hot编码

**缺失值处理**：
- 时序插值：对连续缺失的时序数据使用线性插值
- 邻居填充：基于图结构用邻居节点均值填充
- 模型预测：使用简单模型预测缺失的PTDF值

**特征工程**：

- 时间特征：小时、星期、月份的周期性编码
- 差分特征：功率变化率、负载率变化率
- 聚合特征：区域总负荷、区域平均负载率

#### 图划分策略
**时间划分**：
- 训练集：前70%时间段数据（约2.1年）
- 验证集：中间15%时间段数据（约0.45年）
- 测试集：最后15%时间段数据（约0.45年）

### 模型设计

#### GNN类型选择：GAT（Graph Attention Network）
**选择理由**：
- 注意力机制能够自动学习节点间的重要性权重
- 适合处理电网中节点重要性差异较大的场景
- 可解释性强，注意力权重可以解释节点间的影响关系
- 对图结构变化（如线路检修）具有较好的鲁棒性

#### 输入输出定义
**输入**：
- 节点特征矩阵：$X \in \mathbb{R}^{N \times 48}$
- 边特征矩阵：$E \in \mathbb{R}^{M \times (17+N+5)}$
- 邻接矩阵：$A \in \{0,1\}^{N \times N}$

**输出**：

- 主要输出：线路负载率预测 $\hat{y} \in [0,1]^M$
- 辅助输出：过载概率 $p_{overload} \in [0,1]^M$

**任务类型**：边级回归任务（预测每条边的负载率）

#### 模型结构设计
**特征编码层**：

- 节点编码器：48维 → 128维，使用LayerNorm + ReLU
- 边编码器：(17+N+5)维 → 64维，使用LayerNorm + ReLU
- 全局编码器：系统级特征 → 32维全局上下文

**图注意力层**：

- 第1层GAT：128维 → 128维，8个注意力头，Dropout=0.1
- 第2层GAT：128维 → 128维，6个注意力头，Dropout=0.1
- 第3层GAT：128维 → 64维，4个注意力头，Dropout=0.1
- 残差连接：每层都添加残差连接和LayerNorm

**聚合方式**：
- 多头注意力聚合：$h_i^{(l+1)} = \|_{k=1}^K \sigma(\sum_{j \in N(i)} \alpha_{ij}^k W^k h_j^{(l)})$
- 边特征更新：$e_{ij}^{(l+1)} = MLP([h_i^{(l+1)}, h_j^{(l+1)}, e_{ij}^{(l)}])$

**输出层设计**：

- 边特征聚合：拼接两端节点特征 + 原始边特征
- 预测网络：(64+64+64) → 64 → 32 → 1，使用ReLU + Sigmoid
- 辅助任务头：共享特征 → 过载分类（Sigmoid激活）

#### 损失函数设计

**主要损失**：安全导向负载率预测
$$L_{main} = \frac{1}{M} \sum_{i=1}^{M} w_i (LR_{pred,i} - LR_{true,i})^2$$
其中 $w_i = \{1.0, 2.0, 4.0\}$ 对应不同负载率区间

**载流量约束损失**：
$$L_{ampacity} = \frac{1}{M} \sum_{i=1}^{M} \max(0, LR_{pred,i} - \frac{I_{max,i}}{I_{rated,i}})^2$$

**功率平衡约束损失**：
$$L_{balance} = \left|\sum_{j=1}^{N} P_{gen,j} - \sum_{j=1}^{N} P_{load,j} - P_{loss}\right|^2$$

**辅助任务损失**：
$$L_{aux} = -\frac{1}{M} \sum_{i=1}^{M} [y_{overload,i} \log p_{overload,i} + (1-y_{overload,i}) \log(1-p_{overload,i})]$$

**总损失函数**：
$$L_{total} = L_{main} + 0.3 L_{ampacity} + 0.2 L_{balance} + 0.4 L_{aux}$$

### 训练配置

#### 优化器选择：AdamW
**参数设置**：
- 学习率：$lr = 1 \times 10^{-4}$
- 权重衰减：$weight\_decay = 1 \times 10^{-5}$
- Beta参数：$\beta_1 = 0.9, \beta_2 = 0.999$
- Epsilon：$\epsilon = 1 \times 10^{-8}$

#### 学习率调度策略
**余弦退火调度**：
$$lr_t = lr_{min} + \frac{1}{2}(lr_{max} - lr_{min})(1 + \cos(\frac{t}{T_{max}} \pi))$$
- 最大学习率：$lr_{max} = 1 \times 10^{-4}$
- 最小学习率：$lr_{min} = 1 \times 10^{-6}$
- 重启周期：$T_{max} = 100$ epochs

**预热策略**：
- 前10个epoch线性增长到最大学习率
- 避免训练初期梯度爆炸

#### 正则化方法
**Dropout**：
- GAT层：Dropout率 = 0.1
- 全连接层：Dropout率 = 0.2
- 注意力权重：Dropout率 = 0.1

**LayerNorm**：

- 每个GAT层后添加LayerNorm
- 稳定训练过程，加速收敛

**权重衰减**：
- L2正则化系数：$1 \times 10^{-5}$
- 防止过拟合

**梯度裁剪**：
- 最大梯度范数：0.5
- 防止梯度爆炸

#### 超参数设置
**批处理设置**：
- 批大小：32个子图
- 子图大小：平均50个节点
- 邻居采样：每层采样15个邻居

**训练轮数**：
- 总训练轮数：500 epochs
- 早停耐心：50 epochs
- 验证频率：每5个epoch

### 训练流程

#### 前向传播
**步骤1：特征编码**
- 节点特征编码：将50维原始节点特征通过节点编码器映射为128维统一表示
- 边特征编码：将包含PTDF信息的边特征通过边编码器映射为64维表示
- 全局特征编码：将系统级特征编码为32维全局上下文向量

**步骤2：图汇聚计算**

- 对每个GAT层依次进行前向传播
- 计算多头注意力权重，学习节点间的重要性关系
- 基于注意力权重聚合邻居节点信息，更新节点特征表示
- 应用残差连接和层归一化，保持训练稳定性和梯度流动

**步骤3：边特征聚合**

- 拼接每条线路两端节点的最终嵌入表示和原始边特征
- 通过预测头网络处理聚合后的边特征
- 输出每条线路的负载率预测值，范围在0到1之间

#### 损失计算
**步骤1：主任务损失**
- 计算负载率预测的均方误差损失
- 对过载线路（负载率>95%）应用更高权重，提升过载预测精度
- 确保模型重点关注安全关键的高负载场景

**步骤2：物理约束损失**
- 基于预测的节点状态计算PTDF矩阵
- 与真实PTDF矩阵对比，计算物理一致性误差
- 确保预测结果符合电力系统潮流分布的物理规律

**步骤3：辅助任务损失**
- 计算过载检测的二分类交叉熵损失
- 辅助主任务学习，提升模型对过载场景的敏感性
- 增强模型的多任务学习能力

**步骤4：总损失**
- 将主任务损失、物理约束损失和辅助任务损失按权重组合
- 权重系数分别为1.0、0.1、0.5，平衡不同目标的重要性

#### 反向传播与参数更新
**步骤1：梯度计算**
- 对总损失函数进行反向传播，计算所有网络参数的梯度
- 梯度从输出层逐层传播到输入层，更新所有可训练参数

**步骤2：梯度裁剪**
- 应用梯度范数裁剪，最大范数设为0.5
- 防止梯度爆炸，保持训练过程稳定

**步骤3：参数更新**
- 使用AdamW优化器根据计算的梯度更新网络参数
- 清零梯度缓存，为下一次迭代做准备

**步骤4：学习率调度**
- 根据余弦退火调度策略更新学习率
- 在训练过程中动态调整学习率，提升收敛效果

#### 验证集评估与早停
**验证指标计算**：
- 计算验证集上的RMSE、MAE、MAPE等回归指标
- 计算过载检测的准确率、精确率、召回率等分类指标
- 评估模型在不同负载水平下的预测性能

**早停机制**：
- 监控验证集RMSE，当连续50个epoch无改进时停止训练
- 保存验证性能最佳的模型检查点
- 防止过拟合，提升模型泛化能力

### 模型评估

#### 测试集评估指标
**回归任务指标**：

- **RMSE**：$RMSE = \sqrt{\frac{1}{M} \sum_{i=1}^{M} (y_{pred,i} - y_{true,i})^2}$
- **MAE**：$MAE = \frac{1}{M} \sum_{i=1}^{M} |y_{pred,i} - y_{true,i}|$
- **MAPE**：$MAPE = \frac{1}{M} \sum_{i=1}^{M} \frac{|y_{pred,i} - y_{true,i}|}{y_{true,i}} \times 100\%$
- **R²决定系数**：$R^2 = 1 - \frac{\sum_{i=1}^{M} (y_{pred,i} - y_{true,i})^2}{\sum_{i=1}^{M} (y_{true,i} - \bar{y})^2}$

**分类任务指标**（过载检测）：

- **准确率**：$Accuracy = \frac{TP + TN}{TP + TN + FP + FN}$
- **精确率**：$Precision = \frac{TP}{TP + FP}$
- **召回率**：$Recall = \frac{TP}{TP + FN}$
- **F1分数**：$F1 = \frac{2 \times Precision \times Recall}{Precision + Recall}$
- **AUC-ROC**：接收者操作特征曲线下面积

**物理一致性指标**：

- **PTDF误差**：$Error_{PTDF} = \frac{1}{N \times M} \sum_{i,j} |PTDF_{pred,ij} - PTDF_{true,ij}|$
- **功率平衡误差**：$Error_{balance} = |\sum P_{gen} - \sum P_{load} - P_{loss}|$

#### 消融实验设计
**特征消融实验**：
- 基线模型：仅使用基础电气特征
- +位置编码：添加多层次位置编码
- +PTDF特征：添加PTDF相关特征
- +时间特征：添加时间周期性特征
- 完整模型：使用所有特征

**结构消融实验**：
- 不同层数：1层、2层、3层、4层GAT
- 不同注意力头数：2、4、6、8个头
- 不同隐藏维度：64、128、256维
- 不同聚合方式：mean、max、attention聚合

**损失函数消融实验**：
- 仅MSE损失
- MSE + 加权损失
- MSE + 物理约束损失
- MSE + 辅助任务损失
- 完整损失函数

#### GPU加速优化
**计算优化**：
- 稀疏矩阵运算：利用图的稀疏性加速计算
- 混合精度训练：使用FP16减少内存占用
- 算子融合：合并相邻算子减少内存访问

**内存优化**：
- 梯度累积：小批量累积梯度模拟大批量训练
- 动态图：按需构建计算图减少内存占用
- 内存池：复用内存减少分配开销

### 模型保存

**检查点保存**：

- 保存模型状态字典，包含所有网络层的权重和偏置参数
- 保存优化器状态，包含动量和学习率等优化器内部状态
- 保存学习率调度器状态，确保训练恢复时调度策略连续
- 记录训练轮数、最佳验证指标、超参数配置和特征缩放器
- 使用统一的检查点格式，便于模型版本管理和部署

## Actor网络设计

### 网络架构

#### 共享GNN Backbone
- **复用预训练GNN**：使用负载率预测网络的前三层GAT作为特征提取器
- **特征维度**：输出64维节点嵌入和边嵌入
- **参数共享策略**：预训练参数作为初始化，允许微调优化

#### 策略生成网络
- **状态聚合层**：将节点和边嵌入聚合为全局状态表示
  - 节点聚合：基于重要性加权平均
  - 边聚合：基于负载率加权平均
  - 全局状态：128维向量
- **策略编码层**：
  - 第1层：128 → 256维，ReLU激活
  - 第2层：256 → 128维，ReLU激活
- **动作输出层**：
  - 连续动作头：输出发电机功率调整的均值和标准差
  - 离散动作头：输出开关操作的概率分布

### 动作空间设计

#### 连续动作空间
- **有功功率调整**：各发电机的出力调整量（MW）
- **无功功率调整**：各发电机的无功调整量（Mvar）
- **动作约束**：考虑发电机爬坡速率和出力限制

#### 离散动作空间
- **线路开关操作**：断路器的开合操作
- **变压器分接头调节**：电压调节操作
- **电容器投切**：无功补偿设备操作

### 策略优化目标

#### 安全性目标
- **过载消除**：将所有线路负载率控制在安全范围内
- **电压稳定**：维持节点电压在允许范围内
- **N-1安全**：确保单一设备故障后系统仍安全

#### 经济性目标
- **调度成本最小化**：最小化发电机调整成本
- **网损最小化**：优化潮流分布减少传输损耗
- **可再生能源利用最大化**：优先利用清洁能源

## Critic网络设计

### 网络架构

#### 状态-动作融合
- **状态输入**：来自Actor的128维全局状态表示
- **动作输入**：Actor输出的调度动作向量
- **融合方式**：早期融合，将状态和动作拼接后处理

#### 价值评估网络
- **融合层**：(128 + 动作维度) → 256维，ReLU激活
- **编码层1**：256 → 256维，ReLU激活
- **编码层2**：256 → 128维，ReLU激活
- **输出层**：128 → 1维，输出Q值

### 价值函数设计

#### 即时奖励函数
$$R_t = w_1 R_{safety} + w_2 R_{economic} + w_3 R_{stability}$$

其中：
- $R_{safety} = -\sum_{i} \max(0, LR_i - 0.95)^2$：安全性奖励
- $R_{economic} = -\sum_{j} C_j |\Delta P_j|$：经济性奖励
- $R_{stability} = -\sum_{k} |V_k - V_{ref,k}|$：稳定性奖励

#### 长期价值评估
- **折扣因子**：$\gamma = 0.99$
- **价值函数**：$V(s) = \mathbb{E}[\sum_{t=0}^{\infty} \gamma^t R_{t+1} | s_t = s]$
- **Q函数**：$Q(s,a) = \mathbb{E}[R_{t+1} + \gamma V(s_{t+1}) | s_t = s, a_t = a]$

---

## 建模思路总结与方法论

### 核心创新思路综述

本方案的核心思路可以总结为“多维空间感知 + 物理知识融入 + 安全导向优化”的深度学习范式：

#### 超越传统方法的关键点

**1. 空间维度的重新定义**
```
传统方法：  单一拓扑空间视角
         ↓
本方案：   三重空间融合视角
         ├── 地理空间：环境因子与物理距离
         ├── 电气空间：功率传输与电气耦合
         └── 拓扑空间：网络结构与关键节点
```

**2. 知识与数据的深度融合**
```
传统方法：  纯数据驱动 或 纯物理模型
         ↓
本方案：   物理约束引导的数据学习
         ├── PTDF特征：作为网络输入
         ├── PTDF约束：作为损失函数
         └── PTDF验证：作为评估指标
```

**3. 安全性的系统化考虑**
```
传统方法：  均衡化处理所有预测场景
         ↓
本方案：   安全关键场景重点关注
         ├── 加权损失：过载线路权重×3
         ├── 辅助任务：过载检测并行学习
         └── 注意力正则：防止模型偏见
```

### 技术创新点深度剖析

#### 多层次位置编码的理论基础

**数学原理**：
> 基于几何深度学习理论，高维数据在低维流形上的嵌入表示可以更好地保留数据的内在结构。本方案将电网的多维空间信息通过不同的数学变换（正弦余弦、嵌入学习、图论算法）映射到统一的高维表示空间中，使得GNN能够同时感知和利用这些不同维度的信息。

**工程意义**：
> 这种设计解决了电网GNN应用中的一个根本问题：如何在保持图结构信息完整性的同时，融入非图结构的物理信息。这对于电力系统这种具有强物理约束的复杂系统尤为重要。

#### PTDF物理约束

**优化理论基础**：
> 从优化理论角度，物理约束相当于在损失函数中引入了等式约束，这使得传统的无约束优化问题转化为约束优化问题。通过拉格朗日乘数方法的近似处理（即罚函数方法），将约束条件以平方形式加入损失函数，既保证了数值稳定性，又保持了数学优雅性。

**物理意义深化**：
> PTDF不仅仅是一个数学约束，更是电力系统物理规律的数学抽象。通过将这些物理规律嵌入到深度学习模型中，实际上是在教会神经网络“理解”电力系统的工作原理，而不仅仅是记忆数据模式。

#### 损失函数设计

**简洁性与有效性的平衡**：
> 本方案的损失函数设计体现了“奥卡姆剭刀”原则——在保证功能完整性的前提下，尽可能保持设计的简洁性。四项损失的线性组合不仅数学形式优雅，而且每一项都有明确的物理意义和工程目标。这种设计哲学在复杂工程系统中尤为重要。

#### 数据驱动与知识引导的统一

**传统两种范式的局限：**
- **纯数据驱动**：依赖大量标注数据，缺乏物理约束，可解释性差
- **纯物理建模**：理论基础扎实，但模型复杂度高，难以处理大规模系统

**本方案的融合之道**：
```
数据层面：  利用大规模历史数据学习复杂模式
     +
知识层面：  融入电力系统专业知识保证物理合理性
     =
智能系统：  既具备学习能力又遵循物理定律的AI系统
```

#### 建模流程的系统化设计

**1. 分层递进的特征工程**
```
第一层： 原始特征提取 (静态+动态+环境+时间)
第二层： 位置编码增强 (地理+电气+拓扑)
第三层： 物理约束融入 (PTDF特征+约束)
第四层： 高级特征学习 (GAT网络抽取)
```

**2. 多任务协同的学习策略**
```
主任务： 负载率预测 (连续值回归)
辅助任务： 过载检测 (二分类任务)
物理任务： PTDF一致性 (约束满足)
正则任务： 注意力分布 (模型可解释)
```

**3. 安全导向的训练策略**

```
数据层面： 过采样平衡 + 数据增强
损失层面： 加权损失 + 多目标优化
评估层面： 分层评估 + 消融实验
部署层面： 灰度发布 + 实时监控
```

---

## 总结

本文提出的基于GNN的电网负载率预测建模方法，通过**多维空间位置编码**、**PTDF物理约束融入**和**安全导向复合损失设计**三大核心创新，解决了传统方法在空间相关性建模、物理约束融入和安全性保证等方面的不足。

---

## 附录：电网物理参数集成总结

### 参数集成对照表

| 序号 | 参数名称 | 使用状态 | 集成位置 | 维度变化 | 技术难度 | 优先级 |
|------|----------|----------|----------|----------|----------|--------|
| 1 | 线路最大载流量 | ✅ 新增 | 边特征 | +1维 | 中等 | 高 |
| 2 | 变电站特征 | ✅ 新增 | 节点特征 | +4维 | 较低 | 中 |
| 3 | 线路名称 | ✅ 已有 | 边ID | 0维 | - | - |
| 4 | 负荷率 | ✅ 已有 | 节点+边特征 | 0维 | - | - |
| 5 | 最大负荷时间 | ✅ 新增 | 节点特征 | +1维 | 中等 | 高 |
| 6 | 电压等级 | ✅ 已有 | 节点特征 | 0维 | - | - |
| 7 | 序号（编码） | ✅ 已有 | 节点/边ID | 0维 | - | - |
| 8 | 度夏负荷 | ✅ 新增 | 节点特征 | +1维 | 中等 | 高 |
| 9 | 线额定电流 | ✅ 新增 | 边特征 | +1维 | 较低 | 中 |
| 10 | 线最大载流量 | ✅ 新增 | 边特征 | +2维 | 中等 | 高 |

### 特征维度变化总结

#### 节点特征维度变化
```
原始维度: 50维 → 更新后维度: 48维
├── 静态特征: 6维 (不变)
├── 动态特征: 5维 (不变)
├── 位置编码: 24维 (不变)
├── 间接PTDF统计特征: 5维 (替代原15维PTDF特征)
├── 新增变电站特征: 4维 ⭐
└── 新增时间负荷特征: 4维 ⭐
```

#### 边特征维度变化
```
原始维度: (9+N+5)维 → 更新后维度: (17+N+5)维
├── 电气参数: 5维 (不变)
├── 运行状态: 4维 (不变)
├── PTDF属性: (N+5)维 (不变)
├── 新增载流量特征: 4维 ⭐
└── 新增安全裕度特征: 4维 ⭐
```

### 实施优先级建议

#### 高优先级（立即实施）
1. **载流量特征集成** - 直接影响安全约束建模
2. **时间负荷特征集成** - 显著提升峰值预测精度

#### 中优先级（后续实施）
3. **变电站特征增强** - 提升枢纽节点建模精度
4. **安全裕度特征集成** - 增强风险预警能力

这个集成方案充分考虑了电网物理参数的完整性和重要性，通过系统化的特征工程和模型架构优化，预期将显著提升GNN模型在电网负载率预测任务中的性能表现。

### 总结

本方案通过将PTDF作为节点特征的统计信息输入，重新设计了简洁高效的损失函数，既保留了物理约束的核心作用，又显著降低了计算复杂度，为电网负载率预测提供了更加实用的AI解决方案。
