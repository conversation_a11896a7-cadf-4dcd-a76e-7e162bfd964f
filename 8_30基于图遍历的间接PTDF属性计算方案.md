# 基于图遍历的间接PTDF属性计算方案

## 间接PTDF属性的维度定义

### PTDF矩阵的基本结构

**矩阵维度**：
$$PTDF \in \mathbb{R}^{M \times N}$$

$$
A =
\begin{bmatrix}
a_{11} & a_{12} & \cdots & a_{1n} \\
a_{21} & a_{22} & \cdots & a_{2n} \\
\vdots & \vdots & \ddots & \vdots \\
a_{m1} & a_{m2} & \cdots & a_{mn}
\end{bmatrix}
$$
$A$ 是一个 $m \times n$ 的矩阵，包含 $m$ 行和 $n$ 列，每个元素用 $a_{ij}$ 表示，其中 $i$ 是行号，$j$ 是列号。通过省略号表示矩阵中省略的部分，意味着该矩阵可能非常大，且不必逐个列出每个元素。

- **M**：系统线路总数（行维度）
- **N**：系统节点总数（列维度）
- **元素定义**：$PTDF_{i,j}$ 表示节点j的单位功率注入对线路i潮流的影响系数

## 基于BFS的节点PTDF属性计算

### BFS遍历算法设计

**算法目标**：
通过广度优先搜索遍历节点j的PTDF影响，按影响强度分层计算统计属性。

**BFS遍历流程**：
```
输入：节点j，PTDF矩阵
输出：节点j的5维间接PTDF属性

Step 1: 初始化BFS队列和统计变量
- 创建优先队列，按|PTDF[i,j]|大小排序
- 初始化：max_ptdf=0, sum_ptdf=0, significant_count=0
- 创建访问标记数组：visited[M] = false

Step 2: BFS分层遍历
第一层：遍历高影响线路（|PTDF[i,j]| > 0.1）
- 将所有高影响线路加入队列
- 计算第一层的统计贡献
- 标记为已访问

第二层：遍历中等影响线路（0.01 < |PTDF[i,j]| ≤ 0.1）
- 将中等影响线路加入队列
- 累加统计贡献
- 更新访问标记

第三层：遍历低影响线路（|PTDF[i,j]| ≤ 0.01）
- 处理剩余未访问线路
- 完成统计计算
- 确保遍历完整性

Step 3: 计算间接PTDF属性
- 最大PTDF系数 = max_ptdf
- 平均PTDF系数 = sum_ptdf / M
- PTDF标准差 = 基于分层统计计算
- 主要影响线路数 = significant_count
- 影响集中度 = max_ptdf / (sum_ptdf / M)
```

**BFS的优势**：
- **分层处理**：按影响强度分层，优先处理重要信息
- **早期终止**：可以在处理完重要层次后提前终止
- **内存友好**：队列大小可控，内存使用稳定
- **并行友好**：同一层的线路可以并行处理

## 基于DFS的线路PTDF属性计算

### DFS遍历算法设计

**算法目标**：
通过深度优先搜索遍历线路i的PTDF敏感性，按电气距离深度计算统计属性。

**DFS遍历流程**：
```
输入：线路i，PTDF矩阵，电网拓扑图
输出：线路i的5维间接PTDF属性

Step 1: 初始化DFS栈和统计变量
- 创建DFS栈，按电气距离排序
- 初始化：max_influence=0, sum_influence=0, key_nodes=0
- 创建访问标记：visited[N] = false

Step 2: DFS深度遍历
深度1：遍历直接相邻节点
- 找到与线路i直接相邻的节点
- 计算这些节点的PTDF影响
- 压入栈中，标记已访问

深度2：遍历二度相邻节点
- 从直接相邻节点出发，找到二度相邻节点
- 计算二度节点的PTDF影响
- 累加到统计量中

深度3：遍历更远距离节点
- 继续深度搜索，直到遍历完所有节点
- 按电气距离顺序处理
- 远距离节点影响通常较小

Step 3: 计算间接PTDF属性
- 最大影响系数 = max_influence
- 平均影响系数 = sum_influence / N
- 影响标准差 = 基于深度分层计算
- 关键影响节点数 = key_nodes
- 影响集中度 = max_influence / (sum_influence / N)
```

**DFS的优势**：
- **电气距离感知**：按电气距离深度遍历，符合电网物理特性
- **局部性利用**：相邻节点的PTDF系数通常相关性强
- **缓存友好**：深度遍历提高数据访问的局部性
- **物理直观**：遍历顺序符合电力传输的物理路径

## 间接PTDF属性的物理解释

### 节点属性的电网含义

**属性1：最大PTDF系数**
- **维度**：标量，$\max_i |PTDF_{i,j}| \in \mathbb{R}$
- **物理含义**：节点j对某条线路的最大影响强度
- **电网意义**：识别节点的"关键影响线路"
- **遍历获得**：BFS第一层遍历的最大值

**属性2：平均PTDF系数**
- **维度**：标量，$\frac{1}{M}\sum_i |PTDF_{i,j}| \in \mathbb{R}$
- **物理含义**：节点j对全网的平均影响强度
- **电网意义**：反映节点的"电气重要性"
- **遍历获得**：BFS全层遍历的平均值

**属性3：PTDF标准差**
- **维度**：标量，$\sigma(PTDF_{:,j}) \in \mathbb{R}$
- **物理含义**：节点j影响的分散程度
- **电网意义**：评估影响的"均匀性"
- **遍历获得**：BFS分层统计的方差计算

**属性4：主要影响线路数**
- **维度**：整数，$|\{i: |PTDF_{i,j}| > threshold\}| \in \mathbb{N}$
- **物理含义**：节点j显著影响的线路数量
- **电网意义**：反映节点的"影响范围"
- **遍历获得**：BFS遍历中的阈值计数

**属性5：影响集中度**
- **维度**：标量，$\frac{\max_i |PTDF_{i,j}|}{\frac{1}{M}\sum_i |PTDF_{i,j}|} \in \mathbb{R}$
- **物理含义**：影响是否集中在少数线路
- **电网意义**：识别"瓶颈线路"风险
- **遍历获得**：BFS最大值与平均值的比值

### 线路属性的电网含义

**属性1：最大敏感性系数**
- **维度**：标量，$\max_j |PTDF_{i,j}| \in \mathbb{R}$
- **物理含义**：对线路i影响最大的节点
- **电网意义**：识别线路的"主控节点"
- **遍历获得**：DFS遍历的最大影响值

**属性2：平均敏感性系数**
- **维度**：标量，$\frac{1}{N}\sum_j |PTDF_{i,j}| \in \mathbb{R}$
- **物理含义**：线路i对全网节点的平均敏感性
- **电网意义**：反映线路的"敏感程度"
- **遍历获得**：DFS全深度遍历的平均值

**属性3：敏感性标准差**
- **维度**：标量，$\sigma(PTDF_{i,:}) \in \mathbb{R}$
- **物理含义**：线路i敏感性的分散程度
- **电网意义**：评估影响源的"分布均匀性"
- **遍历获得**：DFS分深度统计的方差计算

**属性4：关键影响节点数**
- **维度**：整数，$|\{j: |PTDF_{i,j}| > threshold\}| \in \mathbb{N}$
- **物理含义**：对线路i有显著影响的节点数量
- **电网意义**：反映线路的"复杂程度"
- **遍历获得**：DFS遍历中的阈值计数

**属性5：敏感性集中度**
- **维度**：标量，$\frac{\max_j |PTDF_{i,j}|}{\frac{1}{N}\sum_j |PTDF_{i,j}|} \in \mathbb{R}$
- **物理含义**：线路是否主要受少数节点控制
- **电网意义**：识别"单点控制"风险
- **遍历获得**：DFS最大值与平均值的比值

## BFS算法计算节点间接PTDF属性

### BFS遍历的基本思路

**遍历原理**：
将PTDF列向量的遍历转化为图的广度优先搜索，按影响强度分层处理，优先计算高影响线路的贡献。

**图构建方法**：
- **节点**：以线路作为图节点
- **边权重**：以|PTDF[i,j]|作为边权重
- **遍历目标**：从节点j出发，遍历所有受影响的线路

### BFS遍历的详细流程

**第一阶段：队列初始化**
1. 创建BFS队列，用于存储待处理的线路
2. 将所有线路按|PTDF[i,j]|大小排序
3. 设置影响强度阈值：高影响(>0.1)、中影响(0.01-0.1)、低影响(<0.01)
4. 初始化统计变量：最大值、累加和、计数器、影响值列表

**第二阶段：分层BFS遍历**
1. **第一层遍历**：处理高影响线路
   - 将|PTDF[i,j]| > 0.1的线路加入队列
   - 逐个出队处理，更新最大值和累加和
   - 记录高影响线路的索引和数量
   - 这一层通常包含最关键的影响信息

2. **第二层遍历**：处理中等影响线路
   - 将0.01 < |PTDF[i,j]| ≤ 0.1的线路加入队列
   - 继续更新统计变量
   - 累加中等影响的贡献
   - 扩展影响范围的统计

3. **第三层遍历**：处理低影响线路
   - 将|PTDF[i,j]| ≤ 0.01的线路加入队列
   - 完成剩余统计计算
   - 确保遍历的完整性
   - 处理长尾影响

**第三阶段：属性计算**
1. 基于BFS遍历结果计算5个间接PTDF属性
2. 验证计算结果的合理性
3. 进行必要的归一化处理
4. 输出最终的属性向量

### BFS遍历的电网物理意义

**分层遍历的物理解释**：
- **第一层**：直接强影响，对应电气距离近的线路
- **第二层**：间接中等影响，对应电气距离中等的线路  
- **第三层**：远程弱影响，对应电气距离远的线路

**优势分析**：
- **重要性优先**：优先处理对节点最重要的线路
- **计算高效**：可以在处理完重要层次后提前终止
- **物理合理**：遍历顺序符合电力影响的传播规律
- **结果稳定**：分层处理提高数值计算的稳定性

## 基于DFS的线路间接PTDF属性计算

### DFS遍历的基本思路

**遍历原理**：
将PTDF行向量的遍历转化为图的深度优先搜索，按电气距离深度处理，模拟功率影响的传播路径。

**图构建方法**：
- **节点**：以电网节点作为图节点
- **边权重**：以|PTDF[i,j]|作为边权重
- **遍历目标**：从线路i出发，深度遍历所有影响源节点

### DFS遍历的详细流程

**第一阶段：栈初始化**
1. 创建DFS栈，用于存储待处理的节点
2. 根据电网拓扑确定节点的电气距离
3. 选择与线路i直接相邻的节点作为起始点
4. 初始化统计变量：最大影响、累加和、关键节点计数

**第二阶段：深度DFS遍历**
1. **深度1**：处理直接相邻节点
   - 将线路i的端节点压入栈
   - 计算这些节点的PTDF[i,j]值
   - 更新最大影响和累加统计
   - 标记为已访问

2. **深度2**：处理二度相邻节点
   - 从栈顶节点出发，找到其相邻节点
   - 计算二度节点的PTDF影响
   - 继续深度搜索
   - 累加统计贡献

3. **深度N**：处理所有剩余节点
   - 继续深度搜索直到遍历完所有节点
   - 处理远距离节点的微弱影响
   - 完成统计计算
   - 确保遍历完整性

**第三阶段：属性计算**
1. 基于DFS遍历的深度信息计算属性
2. 考虑电气距离对统计结果的影响
3. 进行深度加权的统计计算
4. 输出线路的间接PTDF属性

### DFS遍历的电网物理意义

**深度遍历的物理解释**：
- **深度1**：直接电气连接，影响最强最直接
- **深度2**：通过一个中间节点的间接影响
- **深度N**：多级传递的远程影响，通常很微弱

**优势分析**：
- **路径感知**：考虑功率影响的传播路径
- **距离加权**：近距离节点的影响权重更大
- **物理直观**：遍历路径对应实际的电力传输路径
- **局部性强**：相邻节点的处理具有很强的相关性



## 总结

通过图遍历算法计算间接PTDF属性的方案，成功将高维PTDF矩阵转化为低维但信息丰富的统计特征：

1. **维度明确**：节点属性5×1维，线路属性5×1维
2. **算法清晰**：BFS按强度分层，DFS按距离深度
3. **物理合理**：每个属性都有明确的电网物理含义
4. **计算高效**：遍历算法简单高效，适合工程应用

**算法选择**：
- **实时应用**：推荐BFS算法，计算速度快
- **精度要求高**：推荐DFS算法，考虑电气距离
- **大规模电网**：推荐BFS，适合分层处理
- **小规模电网**：推荐DFS，充分利用详细信息

为GNN模型提供了实用的PTDF特征提取方法。
