# 复杂网络非图论指标在电网建模中的应用

## 基于电力系统物理量的网络指标

### 电气阻抗特性指标

**节点阻抗中心性**：
- **定义**：$Z_{center,j} = \frac{1}{\sum_{k \neq j} Z_{jk}}$
- **物理含义**：节点j到其他所有节点的电气距离倒数和
- **电网意义**：阻抗中心性高的节点电气位置优越，电压控制效果好
- **应用价值**：指导无功补偿设备、调相机的最优配置位置

**系统阻抗均匀性**：
- **定义**：$U_{impedance} = 1 - \frac{\text{std}(Z_{jk})}{\text{mean}(Z_{jk})}$
- **物理含义**：电网中阻抗分布的均匀程度
- **电网意义**：均匀性高表示电网结构合理，无明显薄弱环节
- **应用价值**：评估电网规划的合理性和均衡性

### 功率流动特性指标

**功率流集中度**：
- **定义**：$C_{power} = \frac{\sum_i P_i^2}{(\sum_i P_i)^2}$
- **物理含义**：功率流在网络中的集中程度
- **电网意义**：集中度高表示功率传输路径单一，存在瓶颈风险
- **应用价值**：识别功率传输的关键通道和潜在瓶颈

**传输损耗效率**：
- **定义**：$\eta_{loss} = 1 - \frac{\sum_i R_i I_i^2}{P_{total}}$
- **物理含义**：网络功率传输的损耗效率
- **电网意义**：效率高表示网络传输经济性好
- **应用价值**：评估电网运行的经济性和优化潜力

## 基于系统动力学的网络指标

### 频率稳定性指标

**频率同步强度**：
- **定义**：$S_{freq} = 1 - \frac{\text{std}(f_i)}{\text{mean}(f_i)}$
- **物理含义**：各节点频率的同步程度
- **电网意义**：同步强度高表示系统频率稳定性好
- **应用价值**：评估系统小干扰稳定性和频率调节能力

### 电压稳定性指标

**电压梯度分布**：
- **定义**：$\nabla V_j = \sqrt{\sum_{k \in N(j)} (V_j - V_k)^2}$
- **物理含义**：节点j周围的电压梯度强度
- **电网意义**：梯度大的区域电压稳定性差，容易失稳
- **应用价值**：识别电压薄弱区域，指导电压支撑设备配置

**电压调节裕度**：
- **定义**：$M_{voltage} = \frac{V_{max} - V_{current}}{V_{max} - V_{min}}$
- **物理含义**：节点电压的调节余量
- **电网意义**：裕度大的节点电压调节能力强
- **应用价值**：评估电压控制的安全裕度

## 基于网络流理论的指标

### 输电能力指标

**最大输电能力**：
- **定义**：$T_{max} = \min_{\text{割集}} \sum_{l \in \text{割集}} P_{l,max}$
- **物理含义**：网络在两点间的最大功率传输能力
- **电网意义**：反映电网的输电极限和传输瓶颈
- **应用价值**：指导输电网扩建和容量规划

**传输路径多样性**：
- **定义**：$D_{path} = \frac{\text{有效传输路径数}}{\text{理论最大路径数}}$
- **物理含义**：功率传输路径的多样性程度
- **电网意义**：路径多样性高的网络抗故障能力强
- **应用价值**：评估网络的冗余度和可靠性

### 负载分布指标

**负载均衡度**：
- **定义**：$B_{load} = 1 - \frac{\text{std}(\rho_i)}{\text{mean}(\rho_i)}$
- **物理含义**：各线路负载率的均衡程度
- **电网意义**：均衡度高表示网络容量利用合理
- **应用价值**：评估调度策略的负载分配效果

**容量利用效率**：
- **定义**：$\eta_{capacity} = \frac{\sum_i P_i}{\sum_i P_{i,max}}$
- **物理含义**：网络容量的实际利用率
- **电网意义**：利用率适中表示既充分利用又保持安全裕度
- **应用价值**：优化电网的经济运行水平

## 基于信息熵理论的指标

### 功率分布熵指标

**节点功率分布熵**：
- **定义**：$H_{power} = -\sum_j \frac{P_j}{P_{total}} \log \frac{P_j}{P_{total}}$
- **物理含义**：功率在各节点分布的均匀程度
- **电网意义**：熵值高表示功率分布分散，系统鲁棒性好
- **应用价值**：指导分布式电源配置和负荷分布优化

**线路潮流分布熵**：
- **定义**：$H_{flow} = -\sum_i \frac{|P_{i}|}{P_{total}} \log \frac{|P_{i}|}{P_{total}}$
- **物理含义**：潮流在各线路的分布均匀程度
- **电网意义**：分布熵高表示没有过度依赖某些输电通道
- **应用价值**：评估电网传输路径的多样性和安全性

### 电压分布熵指标

**电压水平分布熵**：

- **定义**：$H_{voltage} = -\sum_j \frac{V_j}{\sum_k V_k} \log \frac{V_j}{\sum_k V_k}$
- **物理含义**：电压在各节点的分布特性
- **电网意义**：电压分布熵反映电压调节的协调性
- **应用价值**：评估电压控制策略的整体效果

## 基于网络韧性理论的指标

### 故障传播特性指标

**级联故障放大系数**：
- **定义**：$A_{cascade} = \frac{\text{最终停电节点数}}{\text{初始故障节点数}}$
- **物理含义**：故障影响的放大程度
- **电网意义**：放大系数大的网络级联故障风险高
- **应用价值**：评估网络抗级联故障能力，设计保护策略

**故障隔离效果**：
- **定义**：$I_{isolation} = 1 - \frac{\text{故障影响范围}}{\text{网络总规模}}$
- **物理含义**：网络对故障的隔离能力
- **电网意义**：隔离效果好的网络局部故障不会扩散
- **应用价值**：指导保护系统和网络分区设计

### 系统恢复能力指标

**恢复时间指标**：
- **定义**：$T_{recovery} = \int_0^{\infty} \frac{|P(t) - P_{steady}|}{P_{steady}} dt$
- **物理含义**：系统从故障恢复到稳态的时间积分
- **电网意义**：恢复时间短的系统韧性好
- **应用价值**：评估电网的自愈能力和应急响应能力

**恢复路径冗余度**：
- **定义**：$R_{redundancy} = \frac{\text{可用恢复路径数}}{\text{最少需要路径数}}$
- **物理含义**：系统恢复的路径冗余程度
- **电网意义**：冗余度高的系统恢复选择多，可靠性好
- **应用价值**：指导应急预案制定和恢复策略设计

## 属性分类：节点、边、全局属性

### 节点属性（Node-level Attributes）

**电气特性节点属性**：
1. **节点阻抗中心性** - 标量，反映节点的电气中心地位
2. **电压梯度分布** - 标量，反映节点周围的电压稳定性
3. **电压调节裕度** - 标量，反映节点的电压调节能力

**功率特性节点属性**：
4. **节点功率分布熵** - 标量，反映该节点功率分配的重要性

**维度**：每个节点具有4个标量属性，总维度为 $\mathbb{R}^{4 \times N}$

### 边属性（Edge-level Attributes）

**传输特性边属性**：
1. **传输损耗效率** - 标量，反映该线路的传输经济性
2. **负载均衡度** - 标量，反映该线路的负载水平
3. **容量利用效率** - 标量，反映该线路的容量利用情况

**流动特性边属性**：
4. **线路潮流分布熵** - 标量，反映该线路在潮流分布中的重要性
5. **传输路径多样性** - 标量，反映通过该线路的路径多样性

**维度**：每条边具有5个标量属性，总维度为 $\mathbb{R}^{5 \times M}$

### 全局属性（Global-level Attributes）

**系统稳定性全局属性**：
1. **频率同步强度** - 标量，反映整个系统的频率协调性
2. **系统阻抗均匀性** - 标量，反映整个网络的阻抗分布均匀性
3. **电压水平分布熵** - 标量，反映全网电压分布的协调性

**系统安全性全局属性**：
4. **级联故障放大系数** - 标量，反映系统的级联故障风险
5. **故障隔离效果** - 标量，反映系统的故障隔离能力
6. **恢复时间指标** - 标量，反映系统的整体恢复能力

**系统经济性全局属性**：
7. **功率流集中度** - 标量，反映全网功率流的集中程度
8. **最大输电能力** - 标量，反映系统的整体输电极限
9. **恢复路径冗余度** - 标量，反映系统恢复的路径冗余性

**维度**：全局属性共9个标量，总维度为 $\mathbb{R}^{9 \times 1}$

## GNN模型中的属性应用

### 特征集成策略

**节点特征增强**：
- **原有特征**：48维基础电气特征
- **新增特征**：5维非图论节点属性
- **总维度**：53维节点特征向量

**边特征增强**：
- **原有特征**：线路基础参数（阻抗、容量等）
- **新增特征**：5维非图论边属性
- **总维度**：增强的边特征向量

**全局特征应用**：
- **图级特征**：9维全局属性作为图级特征
- **注意力权重**：全局属性可用于调节注意力机制
- **损失函数**：全局属性可作为额外的约束项

### 计算复杂度分析

**节点属性计算**：
- **时间复杂度**：O(N²)，需要计算节点间的电气距离
- **空间复杂度**：O(N)，存储N个节点的5维属性
- **更新频率**：网络拓扑变化时更新

**边属性计算**：
- **时间复杂度**：O(M)，基于线路的潮流和参数计算
- **空间复杂度**：O(M)，存储M条边的5维属性
- **更新频率**：运行状态变化时实时更新

**全局属性计算**：
- **时间复杂度**：O(N+M)，基于全网统计计算
- **空间复杂度**：O(1)，只有9个标量
- **更新频率**：每次预测时计算

## 指标来源与创新性分析

### 指标来源分类

**已存在的经典指标**：
1. **最大输电能力** - 基于经典的最大流最小割理论，电力系统标准分析方法
2. **负载均衡度** - 基于统计学的变异系数概念，电网经济调度中的标准指标
3. **容量利用效率** - 电力系统中的标准经济指标，广泛用于电网规划
4. **功率分布熵** - 基于信息论的熵概念应用到功率分布，有相关文献支撑

**自定义改造指标**：
1. **节点阻抗中心性** - 将图论中心性概念改造为基于电气阻抗的中心性
2. **电压梯度分布** - 借鉴物理学梯度概念，定义的电压稳定性评估指标
3. **频率同步强度** - 基于同步理论，定义的频率协调性量化指标
4. **级联故障放大系数** - 基于故障分析理论，定义的风险评估指标
5. **恢复路径冗余度** - 基于韧性理论，定义的恢复能力评估指标

**现有概念的重新表达**：

1. **传输损耗效率** - 将传统网损计算转化为节点/线路效率评估指标
2. **故障隔离效果** - 将保护系统隔离概念量化为网络韧性指标

### 理论基础与创新性

**成熟的理论基础**：
- **信息论**：熵的概念和计算方法
- **网络流理论**：最大流最小割算法
- **动力学理论**：同步性和稳定性分析
- **统计学理论**：方差、标准差等统计量

**创新性体现**：
- **电网专用化**：将通用理论专门化为电力系统应用
- **物理量导向**：直接使用电气量而非抽象的拓扑量
- **多层次集成**：节点-边-全局的层次化特征设计
- **工程实用性**：确保指标可以在实际电网中计算和应用

## 总结

通过将非图论指标按节点、边、全局三个层次分类，为GNN模型提供了结构化的特征增强方案：

**特征维度**：
- 节点属性：4×N维
- 边属性：5×M维
- 全局属性：9×1维

**指标特性**：
- 部分基于成熟理论的经典指标
- 部分是针对电网特点的创新定义
- 所有指标都有明确的电力系统物理意义

**应用价值**：为电网GNN建模提供了更贴近电力系统实际的特征描述方法
