# 马尔可夫多阶耦合模型构建

## 模型概述

本模型采用多阶马尔可夫链来建模承载载体（如电力系统）在多种灾害耦合作用下的状态演化过程。与混合马尔可夫模型不同，该模型直接使用单一的多阶马尔可夫链，通过历史状态序列来预测系统未来状态。

## 状态空间定义

### 灾害状态空间
定义多种灾害的联合状态空间：
$$
S_t^{\text{hazard}} = [h_1^t, h_2^t, ..., h_m^t]
$$

其中：
- $h_i^t$ 表示第i种灾害在时间t的强度等级
- 例如：风灾 $h_1 \in \{0, 1, 2, 3\}$，雨灾 $h_2 \in \{0, 1, 2, 3\}$

### 目标系统状态空间
定义承载载体的状态等级：
$$
S_t^{\text{target}} \in \{0, 1, 2, 3, 4\}
$$

其中：
- 0: 正常运行
- 1: 轻度影响
- 2: 中度影响  
- 3: 重度影响
- 4: 严重故障

## 多阶马尔可夫链构建

### 模型基本假设
k阶马尔可夫假设：系统在时间t+1的状态只依赖于前k个时间步的状态信息。

$$
P(S_{t+1}^{\text{target}} | S_1^{\text{target}}, ..., S_t^{\text{target}}, S_1^{\text{hazard}}, ..., S_t^{\text{hazard}}) = P(S_{t+1}^{\text{target}} | S_{t-k+1}^{\text{target}}, ..., S_t^{\text{target}}, S_{t-k+1}^{\text{hazard}}, ..., S_t^{\text{hazard}})
$$

### 转移概率估计

#### K的含义与重要性

**K的物理含义**：
- **K**表示马尔可夫链的阶数，即系统"记忆长度"
- **K=1**：一阶马尔可夫链，系统下一状态只依赖当前状态
- **K=2**：二阶马尔可夫链，系统下一状态依赖当前状态和前一状态
- **K=k**：k阶马尔可夫链，系统下一状态依赖前k个历史状态

**K值的影响**：
- **K值过小**：模型过于简化，无法捕捉复杂的时序依赖关系
- **K值过大**：模型过于复杂，容易过拟合，计算量指数增长
- **最优K值**：在模型复杂度和预测精度之间找到平衡点

#### 使用AIC准则确定最优K值

**AIC准则的基本原理**：
AIC（Akaike Information Criterion）平衡模型的拟合优度和复杂度：
$$AIC = -2\log L + 2p$$

其中：
- $L$：模型的最大似然值
- $p$：模型参数个数
- 第一项：衡量模型拟合优度（越大越好）
- 第二项：惩罚模型复杂度（参数越多惩罚越大）

**AIC确定K值的具体步骤**：

```
Step 1: 设定K值候选范围
K_candidates = [1, 2, 3, 4, 5]

Step 2: 对每个K值训练模型
For each k in K_candidates:
    # 计算参数个数
    p_k = |S_target| × |S_hazard|^k × (|S_target| - 1)

    # 训练k阶马尔可夫模型
    model_k = train_markov_model(data, k)

    # 计算对数似然
    log_L_k = calculate_log_likelihood(model_k, data)

    # 计算AIC值
    AIC_k = -2 × log_L_k + 2 × p_k

Step 3: 选择AIC最小的K值
optimal_k = argmin(AIC_k)
```

**似然函数的计算**：
$$\log L = \sum_{t=k}^{T} \log P(S_{t+1}^{\text{target}} | S_t^{\text{target}}, S_t^{\text{hazard}}, ..., S_{t-k+1}^{\text{hazard}})$$

**其他模型选择准则**：
- **BIC准则**：$BIC = -2\log L + p\log n$（对复杂度惩罚更严格）
- **交叉验证**：比较不同k值下的预测准确率

#### 计算k阶条件转移频次
$$
N_{i,h_t,...,h_{t-k+1},j} = \text{count}(S_t^{\text{target}}=i, S_t^{\text{hazard}}=h_t, ..., S_{t-k+1}^{\text{hazard}}=h_{t-k+1}, S_{t+1}^{\text{target}}=j)
$$

**公式解释**：
- 统计在当前系统状态为i，且当前及过去k-1个时间步的灾害状态序列为$(h_t, h_{t-1}, ..., h_{t-k+1})$的条件下，系统下一步转移到状态j的次数

#### 估计k阶转移概率
$$
P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{\text{hazard}}=h_t, ..., S_{t-k+1}^{\text{hazard}}=h_{t-k+1}) = \frac{N_{i,h_t,...,h_{t-k+1},j}}{\sum_j N_{i,h_t,...,h_{t-k+1},j}}
$$

#### 贝叶斯平滑处理
为避免零概率问题，应用拉普拉斯平滑：
$$
P_{\text{smooth}}(j|i,h_t,...,h_{t-k+1}) = \frac{N_{i,h_t,...,h_{t-k+1},j} + \alpha}{\sum_j N_{i,h_t,...,h_{t-k+1},j} + \alpha \cdot |S^{\text{target}}|}
$$

其中$\alpha$为平滑参数，通常取0.1-1.0。

## 模型训练流程

### 数据预处理
1. **时间对齐**：确保灾害数据和系统响应数据时间同步
2. **缺失值处理**：使用插值或前向填充方法
3. **状态离散化**：将连续观测值映射到离散状态空间

### 参数估计步骤

```
输入：历史数据 {(h_{1t}, h_{2t}, ..., i_t, j_t)}_{t=1}^T
输出：转移概率张量 P[i, h_t, ..., h_{t-k+1}, j]

1. 确定模型阶数k（详细步骤）
   a) 设定候选K值范围：K_candidates = [1, 2, 3, 4, 5]
   b) 对每个k值：
      - 计算参数个数：p_k = |S_target| × |S_hazard|^k × (|S_target| - 1)
      - 训练k阶马尔可夫模型
      - 计算对数似然：log_L_k = Σ log P(S_{t+1} | 历史状态)
      - 计算AIC：AIC_k = -2×log_L_k + 2×p_k
   c) 选择AIC最小的k值：optimal_k = argmin(AIC_k)

2. 构建状态转移计数表（详细步骤）
   a) 初始化计数张量：
      Count[i, h_t, ..., h_{t-k+1}, j] = 0
      维度：|S_target| × |S_hazard|^k × |S_target|

   b) 遍历历史数据：
      For t = k to T-1:
         current_target_state = S_t^target
         next_target_state = S_{t+1}^target
         hazard_sequence = [S_t^hazard, S_{t-1}^hazard, ..., S_{t-k+1}^hazard]

         # 增加对应的计数
         Count[current_target_state, hazard_sequence, next_target_state] += 1

   c) 处理数据稀疏性：
      - 记录零计数的状态组合
      - 为后续平滑处理做准备

3. 计算转移概率矩阵（详细步骤）
   a) 应用拉普拉斯平滑：
      For each (i, hazard_seq, j):
         Smooth_Count[i, hazard_seq, j] = Count[i, hazard_seq, j] + α

   b) 归一化处理：
      For each (i, hazard_seq):
         total = Σ_j Smooth_Count[i, hazard_seq, j]
         For each j:
            P[i, hazard_seq, j] = Smooth_Count[i, hazard_seq, j] / total

   c) 验证概率性质：
      - 检查每行概率和是否为1
      - 检查是否存在负概率

4. 模型验证
   - 在测试集上评估预测准确率
   - 计算对数似然值
   - 验证K值选择的合理性
```

### AIC确定K值的实际示例

**假设场景**：
- 目标系统状态数：|S_target| = 5
- 灾害状态数：|S_hazard| = 4（如风灾4级×雨灾4级 = 16种组合）
- 历史数据长度：T = 1000

**不同K值的参数个数**：
- K=1：p₁ = 5 × 16¹ × 4 = 320个参数
- K=2：p₂ = 5 × 16² × 4 = 5,120个参数
- K=3：p₃ = 5 × 16³ × 4 = 81,920个参数

**AIC计算示例**：
```
假设计算结果：
K=1: log_L₁ = -800,  AIC₁ = -2×(-800) + 2×320 = 2240
K=2: log_L₂ = -750,  AIC₂ = -2×(-750) + 2×5120 = 11740
K=3: log_L₃ = -740,  AIC₃ = -2×(-740) + 2×81920 = 165320

选择：optimal_k = 1 (AIC最小)
```

**K值选择的实际考虑**：
- **数据量限制**：K值过大时，某些状态组合可能没有足够样本
- **计算资源**：高阶模型的存储和计算需求指数增长
- **物理意义**：K值应该符合系统的实际记忆特性

### 状态转移矩阵构建的完整示例

**假设场景**：
- 目标系统状态：S_target ∈ {0, 1, 2}（3个状态）
- 灾害状态：S_hazard ∈ {0, 1}（2个状态）
- K = 1（一阶马尔可夫链）
- 历史数据：[(灾害状态, 当前系统状态, 下一系统状态), ...]

**步骤1：数据准备**
```
历史数据示例：
t=1: (h=0, s=0, s_next=0)
t=2: (h=1, s=0, s_next=1)
t=3: (h=1, s=1, s_next=2)
t=4: (h=0, s=2, s_next=1)
t=5: (h=0, s=1, s_next=0)
...
```

**步骤2：构建计数表**
```
初始化：Count[i, h, j] = 0，维度为 3×2×3

遍历数据统计：
Count[0, 0, 0] += 1  # 当前状态0，灾害0，转移到状态0
Count[0, 1, 1] += 1  # 当前状态0，灾害1，转移到状态1
Count[1, 1, 2] += 1  # 当前状态1，灾害1，转移到状态2
Count[2, 0, 1] += 1  # 当前状态2，灾害0，转移到状态1
Count[1, 0, 0] += 1  # 当前状态1，灾害0，转移到状态0
...

最终计数表（示例）：
Count[0, 0, :] = [3, 1, 0]  # 状态0在灾害0下的转移计数
Count[0, 1, :] = [1, 4, 2]  # 状态0在灾害1下的转移计数
Count[1, 0, :] = [2, 2, 1]  # 状态1在灾害0下的转移计数
Count[1, 1, :] = [0, 1, 3]  # 状态1在灾害1下的转移计数
Count[2, 0, :] = [1, 3, 1]  # 状态2在灾害0下的转移计数
Count[2, 1, :] = [2, 1, 1]  # 状态2在灾害1下的转移计数
```

**步骤3：应用拉普拉斯平滑**
```
设置平滑参数：α = 0.1

Smooth_Count[0, 0, :] = [3+0.1, 1+0.1, 0+0.1] = [3.1, 1.1, 0.1]
Smooth_Count[0, 1, :] = [1+0.1, 4+0.1, 2+0.1] = [1.1, 4.1, 2.1]
...
```

**步骤4：归一化得到转移概率**

```
P[0, 0, :] = [3.1, 1.1, 0.1] / (3.1+1.1+0.1) = [0.721, 0.256, 0.023]
P[0, 1, :] = [1.1, 4.1, 2.1] / (1.1+4.1+2.1) = [0.153, 0.569, 0.278]
P[1, 0, :] = [2.1, 2.1, 1.1] / (2.1+2.1+1.1) = [0.404, 0.404, 0.192]
P[1, 1, :] = [0.1, 1.1, 3.1] / (0.1+1.1+3.1) = [0.023, 0.256, 0.721]
P[2, 0, :] = [1.1, 3.1, 1.1] / (1.1+3.1+1.1) = [0.212, 0.596, 0.212]
P[2, 1, :] = [2.1, 1.1, 1.1] / (2.1+1.1+1.1) = [0.488, 0.256, 0.256]
```

**最终转移概率矩阵**：

```
P[当前状态, 灾害状态, 下一状态] = 转移概率

状态0在不同灾害下的转移概率：
- 灾害0: [0.721, 0.256, 0.023] → 主要保持在状态0
- 灾害1: [0.153, 0.569, 0.278] → 主要转移到状态1

状态1在不同灾害下的转移概率：
- 灾害0: [0.404, 0.404, 0.192] → 在状态0和1间均匀分布
- 灾害1: [0.023, 0.256, 0.721] → 主要转移到状态2

状态2在不同灾害下的转移概率：
- 灾害0: [0.212, 0.596, 0.212] → 主要转移到状态1
- 灾害1: [0.488, 0.256, 0.256] → 主要转移到状态0
```

## 预测算法

### 一步预测
给定当前状态和历史信息，预测下一时刻状态：

$$
P(S_{t+1}^{\text{target}}=j | \text{当前状态}) = P(j | S_t^{\text{target}}, S_t^{\text{hazard}}, ..., S_{t-k+1}^{\text{hazard}})
$$

### 多步预测
采用迭代方法进行多步预测：

```python
def multi_step_prediction(current_state, hazard_sequence, steps, transition_matrix):
    """
    多步预测算法
    
    参数:
    - current_state: 当前系统状态
    - hazard_sequence: 未来灾害状态序列
    - steps: 预测步数
    - transition_matrix: 训练好的转移概率矩阵
    """
    state_prob = [0] * num_states
    state_prob[current_state] = 1.0
    
    for step in range(steps):
        next_state_prob = [0] * num_states
        hazard_state = hazard_sequence[step]
        
        for i in range(num_states):
            if state_prob[i] > 0:
                for j in range(num_states):
                    # 查询转移概率矩阵
                    transition_prob = transition_matrix[i, hazard_state, j]
                    next_state_prob[j] += state_prob[i] * transition_prob
        
        state_prob = next_state_prob
    
    return state_prob
```

## 模型评估

### 评估指标
1. **预测准确率**：$\text{Accuracy} = \frac{\text{正确预测数}}{\text{总预测数}}$
2. **对数似然**：$\log L = \sum_t \log P(S_{t+1}^{\text{obs}} | S_t, h_t, ...)$
3. **混淆矩阵**：分析各状态的预测效果

### 模型选择
- 比较不同阶数k的模型性能
- 平衡模型复杂度和预测精度
- 考虑计算效率和存储需求

## 应用示例

假设k=2的二阶马尔可夫模型：
- 当前系统状态：$S_t^{\text{target}} = 1$
- 当前灾害状态：$(h_1^t, h_2^t) = (2, 1)$
- 历史灾害状态：$(h_1^{t-1}, h_2^{t-1}) = (1, 0)$

预测公式：
$$
P(S_{t+1}^{\text{target}}=j) = P(j | S_t^{\text{target}}=1, h_1^t=2, h_2^t=1, h_1^{t-1}=1, h_2^{t-1}=0)
$$

通过查询训练好的转移概率张量获得各状态的转移概率，选择概率最大的状态作为预测结果。