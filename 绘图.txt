graph TD
    %% 数据输入层
    A["电网运行数据"] --> B["特征工程"]
    A1["拓扑结构"] --> B
    A2["地理信息"] --> B
    A3["气象数据"] --> B
    
    %% 特征层
    B --> C["多层次位置编码"]
    B --> D["PTDF物理特征"]
    B --> E["时序特征"]
    
    %% 位置编码详细
    C --> C1["地理位置编码<br/>PE_geo"]
    C --> C2["电气距离编码<br/>PE_elec(距离分桶)"]
    C --> C3["拓扑结构编码<br/>PE_topo"]
    
    %% GNN预测模型
    C1 --> F["GAT网络"]
    C2 --> F
    C3 --> F
    D --> F
    E --> F
    
    F --> F1["第1层GAT<br/>8个注意力头<br/>直接邻居关系"]
    F1 --> F2["第2层GAT<br/>6个注意力头<br/>二跳邻居关系"]
    F2 --> F3["第3层GAT<br/>4个注意力头<br/>全局结构关系"]
    
    %% 损失函数
    F3 --> G["复合损失函数"]
    G --> G1["主要损失<br/>加权MSE"]
    G --> G2["物理约束损失<br/>PTDF一致性"]
    G --> G3["辅助任务损失<br/>过载检测"]
    G --> G4["注意力正则化<br/>熵正则化"]
    
    %% 预测输出
    F3 --> H["负载率预测"]
    F3 --> H1["过载概率"]
    F3 --> H2["注意力权重解释"]
    
    %% 强化学习部分
    F3 --> I["共享GNN Backbone<br/>特征提取器"]
    
    %% Actor网络
    I --> J["Actor网络"]
    J --> J1["状态聚合层<br/>128维全局状态"]
    J1 --> J2["策略编码层<br/>128→256→128维"]
    J2 --> J3["动作输出层"]
    J3 --> J4["连续动作头<br/>功率调整μ,σ"]
    J3 --> J5["离散动作头<br/>开关操作概率"]
    
    %% Critic网络
    I --> K["Critic网络"]
    J1 --> K
    J4 --> K
    J5 --> K
    K --> K1["状态-动作融合<br/>早期融合"]
    K1 --> K2["价值评估网络<br/>256→256→128→1"]
    K2 --> K3["Q值输出"]
    
    %% SAC算法
    J4 --> L["SAC算法"]
    J5 --> L
    K3 --> L
    L --> L1["策略更新<br/>重参数化技巧"]
    L --> L2["价值函数更新<br/>双Critic网络"]
    L --> L3["温度参数更新<br/>最大熵框架"]
    
    %% 奖励函数
    L --> M["奖励函数设计"]
    M --> M1["安全性奖励<br/>过载消除+电压稳定"]
    M --> M2["经济性奖励<br/>成本最小化+网损优化"]
    M --> M3["稳定性奖励<br/>N-1安全准则"]
    
    %% 最终输出
    H --> N["实时监控"]
    H1 --> N1["风险预警"]
    L1 --> N2["调度决策支持"]
    
    %% 样式定义
    classDef input fill:#e1f5fe,stroke:#01579b
    classDef feature fill:#f3e5f5,stroke:#4a148c
    classDef gnn fill:#e8f5e8,stroke:#1b5e20
    classDef loss fill:#fff3e0,stroke:#e65100
    classDef rl fill:#fce4ec,stroke:#880e4f
    classDef output fill:#f1f8e9,stroke:#33691e
    
    class A,A1,A2,A3 input
    class B,C,C1,C2,C3,D,E feature
    class F,F1,F2,F3 gnn
    class G,G1,G2,G3,G4 loss
    class I,J,J1,J2,J3,J4,J5,K,K1,K2,K3,L,L1,L2,L3,M,M1,M2,M3 rl
    class H,H1,H2,N,N1,N2 output